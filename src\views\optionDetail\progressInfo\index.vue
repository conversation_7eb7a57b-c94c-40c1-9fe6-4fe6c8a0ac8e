<template>
  <div class="content">
    <div class="left-area" v-if="!isShrinked">
      <KeyInformationCard :key-information="keyInfo" />
    </div>
    <div class="shrink-button" :class="{ shrinked: isShrinked }">
      <el-icon v-if="!isShrinked" @click="shrinkLeft(true)"
        ><ArrowLeft
      /></el-icon>
      <el-icon v-if="isShrinked" @click="shrinkLeft(false)"
        ><ArrowRight
      /></el-icon>
    </div>
    <div class="main-area" :class="{ shrinked: isShrinked }">
      <ProgressInformationCard
        :progress-data="progressDataRef"
        @updateProgress="updateProgress"
      />
      <template v-if="hasViewPhaseDetails">
        <component
          :node="currentNode"
          :canEdit="canEdit"
          :is="dynamicComponent"
          @refresh="refresh"
        ></component>
      </template>
      <DetailProgressCard ref="progressCardRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  shallowRef,
  defineAsyncComponent,
  computed,
  watch,
  onMounted,
  defineProps,
  toRefs,
} from "vue";
import KeyInformationCard from "@/components/progressInformation/KeyInformationCard.vue";
import ProgressInformationCard from "@/components/progressInformation/ProgressInformationCard.vue";
import DetailProgressCard from "@/components/progressInformation/DetailProgressCard.vue";
import { useRoute } from "vue-router";
import {
  fetchMaxStepBeforeBack,
  fetchProgressData,
  fetchStatus,
  fetchStepPerson,
} from "@/api/view/optionDetail/detail";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import { progressData } from "@/business/progressInformation/progressInformation";
import { cloneDeep } from "lodash-es";
import { useUserStore } from "@/store/modules/user";
import { useFlowInfoStore } from "@/store";
import { useEventBus } from "@/hooks/useEventBus";
import { ElLoading } from "element-plus";
import router from "@/router";

const userStore = useUserStore();
const userInfo = userStore;

let isShrinked = ref(false);

const shrinkLeft = (isShrink) => {
  isShrinked.value = isShrink;
};

const workFlowStore = useFlowInfoStore();
// 通用权限检查函数
const checkResourcePermission = (resourceId: string) => {
  if (!resourceId || !userStore.menuList) return false;
  // 检查菜单列表中是否包含指定资源ID
  return userStore.menuList.some((menu) => menu.resourceId === resourceId);
};

// 判断是否有导入源声权限
const hasViewPhaseDetails = computed(() => {
  return checkResourcePermission("ViewPhaseDetails");
});
const props = defineProps({
  keyInfo: {
    default: () => [],
    required: true,
  },
});

const { keyInfo } = toRefs(props);

const eventBus = useEventBus();

const route = useRoute();

const progressDataRef = ref(cloneDeep(progressData));

// 当前流程节点
let currentProgressNode = ref("");

const selectNodeName = computed(() => {
  if (progressDataRef.value[0]) {
    let current = progressDataRef.value[0].operations[0].componentName;
    progressDataRef.value.forEach((item) => {
      item.operations.forEach((subItem) => {
        if (subItem.isSelected) {
          if (!currentProgressNode.value) {
            // 第一次加载时存储当前节点
            currentProgressNode.value = subItem.componentName;
          }
          current = subItem.componentName;
        }
      });
    });
    return current;
  } else {
    return "ReplicateProblemCard";
  }
});

const currentNode = computed(() => {
  if (progressDataRef.value[0]) {
    let current = progressDataRef.value[0].operations[0];
    progressDataRef.value.forEach((item) => {
      item.operations.forEach((subItem) => {
        if (subItem.isSelected) {
          current = subItem;
        }
      });
    });
    return current;
  } else {
    return { data: {} };
  }
});

const finalStep = ref({});

watch(
  () => selectNodeName.value,
  () => {
    //获取为selected的node
    loadComponent(selectNodeName.value);
  }
);

const id = ref("0");

const dynamicComponent = shallowRef(null);

function loadComponent(componentName) {
  dynamicComponent.value = defineAsyncComponent(
    () => import(`@/components/progressInformation/${componentName}.vue`)
  );
}

const getPerson = async () => {
  const resp = await fetchStepPerson(id.value);
  workFlowStore.setPersonList(resp);
  let index = 0;
  if (resp && resp.length > 0) {
    for (let i = 0; i < progressDataRef.value.length; i++) {
      for (let j = 0; j < progressDataRef.value[i].operations.length; j++) {
        if (progressDataRef.value[i].operations[j].reject && !progressDataRef.value[i].operations[j].beRejected) {
          return false;
        }
        progressDataRef.value[i].operations[j].operator = resp[index];
        index++;
      }
    }
  }
};

const getStatus = async (step = null) => {
  const res = await fetchStatus(id.value);
  finalStep.value = {step:res.step,status: res.status};
  const index = parseInt(res.status);
  if (parseInt(res.step) > 8) {
    progressDataRef.value[4].isCurrent = true;
    if (!step) {
      progressDataRef.value[4].operations[1].isSelected = true;
    }
    progressDataRef.value[4].operations[1].isCurrent = true;
    currentProgressNode.value =
      progressDataRef.value[4].operations[1].componentName;
  }
  if (index === 0) {
    progressDataRef.value[index].operations[0].isCurrent = true;
    progressDataRef.value[index].operations[0].isSelected = true;
  }
  //是否已关单
  if (parseInt(res.step) > 8) {
    workFlowStore.setIsAllFinished(true);
  } else {
    workFlowStore.setIsAllFinished(false);
  }
  progressDataRef.value[0].duration = computeDays(
    res.createTime,
    index === 0 ? null : res.identifyFinishTime
  );
  if (index > 0) {
    progressDataRef.value[1].duration = computeDays(
      res.identifyFinishTime,
      index === 1 ? null : res.reviewDelimitTime
    );
  }
  if (index > 1) {
    progressDataRef.value[2].duration = computeDays(
      res.delimitFinishTime,
      index === 2 ? null : res.planLockTime
    );
  }
  if (index > 2) {
    progressDataRef.value[3].duration = computeDays(
      res.planLockTime,
      index === 3 ? null : res.repairFinishTime
    );
  }
  if (index > 3) {
    progressDataRef.value[4].duration = computeDays(
      res.repairFinishTime,
      parseInt(res.step) > 8 ? res.closeTime : null
    );
  }
  for (let i = 0; i < progressDataRef.value.length; i++) {
    for (let j = 0; j < progressDataRef.value[i].operations.length; j++) {
      if (progressDataRef.value[i].operations[j].step === step) {
        progressDataRef.value[i].operations[j].isSelected = true;
      }
      if (
        progressDataRef.value[i].status === res.status &&
        progressDataRef.value[i].operations[j].step === res.step
      ) {
        progressDataRef.value[i].isCurrent = true;
        if (!step) {
          progressDataRef.value[i].operations[j].isSelected = true;
        }
        progressDataRef.value[i].operations[j].isCurrent = true;
        currentProgressNode.value =
          progressDataRef.value[i].operations[j].componentName;
        return;
      } else {
        progressDataRef.value[i].operations[j].isFinished = true;
      }
    }
  }
};

const computeDays = (startTime, endTime) => {
  if (startTime && endTime) {
    const num = new Date(endTime).getTime() - new Date(startTime).getTime();
    return parseInt(num / (1000 * 60 * 60 * 24)).toString();
  }
  if (startTime && !endTime) {
    const num = new Date().getTime() - new Date(startTime).getTime();
    return parseInt(num / (1000 * 60 * 60 * 24)).toString();
  }
  return "/";
};

const updateProgress = (data) => {
  progressDataRef.value = data;
};

const resetProgressData = () => {
  progressDataRef.value = cloneDeep(progressData);
};

const progressCardRef = ref();

const refresh = async (step = null, isInit = false) => {
  if (!isInit && !step) {
    router.back();
    return;
  }
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  resetProgressData();
  eventBus.emit("getIssueInfo");
  progressCardRef.value.refresh();
  await getStatus(step);
  await handleReject();
  await getPerson();
  loading.close();
};

// 子卡片是否可以编辑
const canEdit = computed(() => {
  if (userInfo.isAdmin) {
    return true
  }
  const currentUser = userInfo.currentName;
  if (currentProgressNode.value === currentNode.value.componentName && !currentNode.value.isFinished) {
    // 当前节点是流程节点且节点未完成时，登录用户与当前处理人相同才可以编辑
    const currentHandlerOption = keyInfo.value.find((item) => {
      return item.label === "当前处理人";
    });
    const currentHandler = currentHandlerOption?.value;
    return currentHandler === currentUser || currentHandler === "---";
  } else {
    // 当前节点不是流程节点时，登录用户与节点处理人相同才可以编辑
    // 第八步回归问题不允许修改
    return (
      currentNode.value.operator === currentUser &&
      currentNode.value.operation !== "回归问题"
    );
  }
});

const handleReject = async () => {
  const resp = await fetchMaxStepBeforeBack(id.value);
  if (!resp || parseInt(finalStep.value.step) >= parseInt(resp.currentStep)) {
    return;
  }
  for (let i = 0; i < progressDataRef.value.length; i++) {
    for (let j = 0; j < progressDataRef.value[i].operations.length; j++) {
      if(progressDataRef.value[i].operations[j].step >finalStep.value.step){
        if(progressDataRef.value[i].operations[j].step < resp.currentStep) {
          progressDataRef.value[i].operations[j].beRejected = true;
          progressDataRef.value[i].operations[j].reject = true;
        }else if(progressDataRef.value[i].operations[j].step === resp.currentStep){
          progressDataRef.value[i].operations[j].reject = true;
          return;
        }
      }else if(progressDataRef.value[i].operations[j].step ===finalStep.value.step){
        progressDataRef.value[i].operations[j].beRejected = true;
      }
    }
  }
};

onMounted(() => {
  id.value = route.query.id as string;
  refresh(null, true);
  loadComponent(selectNodeName.value);
});
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  background-color: #f1f3f5;
  .left-area {
    position: relative;
    width: 250px;
    box-sizing: border-box;
    align-items: stretch;
    background: white;
    border-radius: 12px;
  }
  .main-area {
    flex-grow: 1;
    margin-left: 20px;
    overflow: hidden;
    &.shrinked {
      margin-left: 0;
    }
  }

  .card {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px;
  }

  :deep(.shrink-button) {
    /* 方向=左, 展开=on, 悬停=off (展开/收起) */
    position: fixed;
    width: 16px;
    height: 64px;
    /* 自动布局 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px;
    left: 282px;
    top: 450px;
    border-radius: 0px 12px 12px 0px;
    /* 内容色/图标、文本反色/一级 */
    background: rgb(255, 255, 255);
    transition: all 0.3s ease;
    .el-icon {
      cursor: pointer;
    }

    .shrink.folded {
      width: 0;
    }

    &.shrinked {
      left: 0;
    }
  }
}
</style>
