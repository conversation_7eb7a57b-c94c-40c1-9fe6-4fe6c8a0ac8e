<template>
  <div class="opinion-manage">
    <div class="page-title-section">
      <div class="page-title">源声场景管理</div>
    </div>
    <!-- 筛选部分 -->
    <div class="filter-comp">
      <!-- 共多选 -->
      <searchArea
        :defaulList="defaulList"
        :otherList="otherList"
        :filterOptions="filterOptions"
        @updateFilterOptions="handleFilterOptions"
      ></searchArea>
      <!-- 单独的筛选 -->
      <div class="table-head">
        <div class="table-header-row">
          <div class="table-head-left">
            <el-input
              v-model.trim="filterObj.appName"
              placeholder="请输入应用名称"
              class="filter-item"
              clearable
              @change="queryData"
            />
            <el-button
              type="primary"
              text
              @click="showAllInputFilter = !showAllInputFilter"
              class="ml-8"
            >
              <span style="width: 50px">{{
                showAllInputFilter ? "收起" : "更多搜索"
              }}</span>
              <el-icon class="el-icon--right" v-show="!showAllInputFilter"
                ><CaretBottom
              /></el-icon>
              <el-icon class="el-icon--right" v-show="showAllInputFilter"
                ><CaretTop
              /></el-icon>
            </el-button>
            <el-button type="primary" class="ml-8" @click="queryData"
              >查询</el-button
            >
            <el-button @click="reasetSearch">重置</el-button>
          </div>
          <div class="table-head-right">
            <el-button @click="importSource(true)" v-if="hasImportPermission"
              >导入源声</el-button
            >
            <el-button
              :loading="exportLoading"
              @click="handleExportRes"
              v-if="hasExportPermission"
              >导出</el-button
            >
            <ColumnSettingDialog
              :allColumn="allColumn"
              :defaultColumn="defaultColumn"
              :localStorageName="LocalStorageEnum.SOURCE_SCENE_MANAGE"
              @saveHandle="setColumn"
            />
          </div>
        </div>
        <div v-if="showAllInputFilter" class="flex-box table-header-row">
          <el-form-item>
            <el-date-picker
              v-model="filterObj.createTime"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              start-placeholder="创建时间"
              end-placeholder="创建时间"
              clearable
              @change="queryData"
              class="filter-item"
              unlink-panels
            />
          </el-form-item>
          <el-form-item class="ml-8 select-product">
            <el-select
              v-model="filterObj.productType"
              placeholder="请选择产品类型"
              class="select-left"
              clearable
              style="margin-right: -1px"
              @change="productTypeChange"
            >
              <el-option
                v-for="item in productTypeList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
            <el-select
              v-model="productModelList"
              multiple
              clearable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="1"
              no-data-text="请先选择产品类型"
              :placeholder="
                !filterObj.productType ? '请选择产品机型' : '请先选择产品机型'
              "
              class="select-right next"
              @change="productModelChange"
            >
              <el-option
                style="display: flex; flex-wrap: nowrap"
                v-for="item in productMap[filterObj.productType]"
                :key="item"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
    </div>
    <!-- 表格部分 -->
    <div class="search-result-table">
      <div class="table-wrap">
        <el-table
          :data="tableData"
          @sort-change="sortChange"
          :default-sort="{ prop: 'totalVolume', order: 'descending' }"
          :header-cell-class-name="(params:any) => {setHeaderClass(params)}"
          ref="manageTable"
          style="width: 100%"
          v-loading="loading"
          :empty-text="TABLE_EMPTY_TXT"
        >
          <el-table-column
            v-for="item in columnList"
            :key="item.prop"
            :sortable="item.sortable"
            :sort-orders="item.sortOrders"
            :type="item.type"
            :prop="item.prop"
            :label="item.label"
            :min-width="item.minWidth"
            show-overflow-tooltip
          >
            <template #header>
              <template v-if="item.headerSearch">
                <div class="col-search-box">
                  <div>{{ item.label }}</div>
                  <el-popconfirm
                    width="220"
                    placement="bottom"
                    :hide-icon="true"
                  >
                    <template #reference>
                      <div>
                        <el-icon
                          class="icon-filter"
                          :color="
                            filterObj[item.prop] ? 'rgb(10, 89, 247)' : '#333'
                          "
                          ><Filter
                        /></el-icon>
                      </div>
                    </template>
                    <template #actions="{ cancel }">
                      <el-input
                        v-model="filterObj[item.prop]"
                        placeholder="请输入关键词搜索"
                        style="margin-bottom: 10px"
                      ></el-input>
                      <el-button type="primary" size="small" @click="queryData">
                        查询
                      </el-button>
                      <el-button
                        size="small"
                        @click="
                          () => {
                            resetSingle(filterObj, item.prop, '');
                            cancel();
                          }
                        "
                        >重置</el-button
                      >
                    </template>
                  </el-popconfirm>
                </div>
              </template>
              <span v-else>{{ item.label }}</span>
            </template>
            <template #default="scope" v-if="item.prop !== 'checkbox'">
              {{
                (item.prop === "automated"
                  ? transAuto(scope.row[item.prop])
                  : item.time
                  ? transTime(scope.row[item.prop])
                  : scope.row[item.prop]) || "---"
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="config"
            label="操作"
            :width="
              hasBlockScenePermission ? 240 : hasEditPermission ? 200 : 180
            "
            fixed="right"
          >
            <template #header>
              <span> 操作 </span>
              <el-tooltip content="来自祥云数据不可编辑" placement="top">
                <el-icon class="question-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
            <template #default="scope">
              <span class="primary-font" @click="gotoDetail(scope.row)"
                >查看源声</span
              >
              <span
                v-if="scope.row.opinionIssueId"
                @click="jumpIssueDetail(scope.row)"
                class="ml-16 primary-font"
                >查看问题</span
              >
              <span
                @click="createProblem(scope.row)"
                :class="
                  hasListSettingPermission
                    ? 'ml-16 primary-font'
                    : 'ml-16 disabled'
                "
                v-else
                >创建问题</span
              >
              <span
                @click="editSourceData(scope.row)"
                v-if="hasEditPermission"
                :class="
                  scope.row.automated ? 'ml-16 disabled' : 'ml-16 primary-font'
                "
                >编辑</span
              >
              <span
                @click="handleBlockSceneDialog(scope.row.sceneId)"
                v-if="hasBlockScenePermission"
                class="ml-16 primary-font"
                >屏蔽</span
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="flex-box j-c-space-between a-i-center mt-12">
          <span class="pagination-total">总计：{{ pageInfo.total }}</span>
          <div class="flex-box a-i-center">
            <el-pagination
              v-model:current-page="pageInfo.pageNo"
              :page-size="pageInfo.pageSize"
              size="small"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              background
              layout="prev, pager, next, sizes, jumper"
              :total="pageInfo.total"
              :page-sizes="pageSizes"
            />
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      title="导入源声"
      v-model="showImportSource"
      width="62.5%"
      style="min-width: 900px"
    >
      <el-form
        ref="originVolumeformRef"
        :model="sourceFormData"
        label-width="109"
        label-position="left"
      >
        <el-row :gutter="60">
          <el-col v-for="item in sourceData" :key="item.key" :span="12">
            <el-form-item
              :prop="item.key"
              :label="item.label"
              :rules="item.rules"
              :class="item.rules?.required ? '' : 'unRequired'"
            >
              <el-autocomplete
                v-if="item.component === 'Autocomplete'"
                :placeholder="item.placeholder || '请输入'"
                v-model.trim="sourceFormData[item.key]"
                :disabled="item.disabled"
                :trigger-on-focus="false"
                :fetch-suggestions="
                  (value, cb) => fetchSuggestions(item.key, value, cb)
                "
                @change="(value) => inputValueChange(item, value)"
                @select="(selectObj) => inputValueChange(item, selectObj.value)"
              />
              <el-input
                v-if="item.component === 'Input'"
                :placeholder="item.placeholder || '请输入'"
                v-model.trim="sourceFormData[item.key]"
                :disabled="item.disabled"
                @change="(value) => inputValueChange(item, value)"
              />
              <el-select
                v-if="item.component === 'Select'"
                placeholder="请选择"
                :multiple="item.multiple"
                collapse-tags
                :max-collapse-tags="1"
                @change="selectChange(item.key, 'import')"
                v-model="sourceFormData[item.key]"
                :filterable="item.filterable"
              >
                <template
                  #header
                  v-if="
                    item.key === 'productModel' || item.key === 'productType'
                  "
                >
                  <el-checkbox
                    v-model="productCheckAll[item.key]"
                    @change="
                      (value) =>
                        checkAllChangeHandle(value, item.key, sourceFormData)
                    "
                    :indeterminate="productIndeterminate[item.key]"
                  >
                    全选
                  </el-checkbox>
                </template>
                <div v-if="item.key === 'productModel'">
                  <el-option
                    v-for="o in importProductModelList"
                    :key="o.value"
                    :label="o.label"
                    :value="o.label"
                  />
                </div>
                <div v-else>
                  <el-option
                    v-for="o in item.option"
                    :key="o.value"
                    :label="o.label"
                    :value="o.value"
                  />
                </div>
              </el-select>
              <el-date-picker
                v-if="item.component === 'DatePicker'"
                placeholder="请选择"
                v-model="sourceFormData[item.key]"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :disabled-date="item.disabledDate"
              />
              <el-radio-group
                v-if="item.component === 'Radio'"
                :disabled="item.disabled"
                v-model="sourceFormData[item.key]"
                @change="(value) => inputValueChange(item, value)"
              >
                <el-radio
                  v-for="o in item.option"
                  :key="o.value"
                  :label="o.label"
                  :value="o.value"
                />
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align: end">
        <el-button @click="showImportSource = false">取消</el-button>
        <el-button
          v-if="!showNextBtn"
          type="primary"
          :loading="importSourceBtn.loading"
          @click="importSourceSubmit('submit')"
          >{{ importSourceBtn.txt }}</el-button
        >
        <!-- 下一步按钮，为了和确定按钮加载时保持一致，使用 importSourceBtn.txt -->
        <el-button
          v-else
          :loading="importSourceBtn.loading"
          type="primary"
          @click="importSourceSubmit('next')"
          >{{ importSourceBtn.txt }}</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="创建问题"
      v-model="showCPublicProblem"
      width="62.5%"
      style="min-width: 900px"
    >
      <el-form
        ref="formRef"
        :model="cPublicProblemFormData"
        label-width="109"
        label-position="left"
      >
        <el-row :gutter="60">
          <el-col
            v-for="item in cPublicProblemFormComponentsData"
            :key="item.key"
            :span="12"
          >
            <el-form-item
              :label="item.label"
              :prop="item.key"
              :rules="item.rules"
              :class="item.rules?.required ? '' : 'unRequired'"
            >
              <el-input
                v-if="item.component === 'Input'"
                placeholder="请输入"
                v-model.trim="cPublicProblemFormData[item.key]"
                :disabled="item.disabled"
              />
              <el-select
                v-if="item.component === 'Select'"
                placeholder="请选择"
                :multiple="item.multiple"
                collapse-tags
                :max-collapse-tags="1"
                @change="selectChange(item.key, 'create')"
                v-model="cPublicProblemFormData[item.key]"
                :filterable="item.filterable"
              >
                <template
                  #header
                  v-if="
                    item.key === 'productModel' || item.key === 'productType'
                  "
                >
                  <el-checkbox
                    v-model="productCheckAll[item.key]"
                    :indeterminate="productIndeterminate[item.key]"
                    @change="
                      (value) =>
                        checkAllChangeHandle(
                          value,
                          item.key,
                          cPublicProblemFormData
                        )
                    "
                  >
                    全选
                  </el-checkbox>
                </template>
                <div v-if="item.key === 'productModel'">
                  <el-option
                    v-for="o in importProductModelList"
                    :label="o.label"
                    :value="o.label"
                    :key="o.value"
                  />
                </div>
                <div v-else>
                  <el-option
                    v-for="o in item.option"
                    :label="o.label"
                    :value="o.value"
                    :key="o.value"
                  />
                </div>
              </el-select>
              <el-date-picker
                v-if="item.component === 'DatePicker'"
                placeholder="请选择"
                :disabled-date="item.disabledDate"
                v-model="cPublicProblemFormData[item.key]"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="text-align: end">
        <el-button @click="showCPublicProblem = false">取消</el-button>
        <el-button v-if="showPreBtn" type="primary" @click="importSource(false)"
          >上一步</el-button
        >
        <el-button type="primary" @click="createProblemSubmit">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="编辑场景"
      v-model="showEditSource"
      width="62.5%"
      style="min-width: 900px"
    >
      <SourceSceneEditForm
        :disabledKeys="disabledKeys"
        :isHideKeys="['description']"
        :rowData="editSourceFormData"
        @submitHandle="editSourceSubmitHandle"
        @cancelHandle="editSourceCancelHandle"
      />
    </el-dialog>

    <!-- 屏蔽场景 -->
    <el-dialog
      title="屏蔽场景"
      v-model="showBlockSceneDialog"
      width="700px"
      align-center
    >
      <el-form
        ref="blockSceneRef"
        :model="blockSceneForm"
        label-width="109"
        label-position="right"
      >
        <el-col>
          <el-form-item
            v-for="col in blockFromCol"
            :label="col.label"
            :prop="col.key"
            :rules="col.rules"
          >
            <el-select
              v-if="col.component === 'Select'"
              v-model="blockSceneForm.blockReason"
              :placeholder="col.placeholder"
            >
              <el-option
                v-for="opt in col.option"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
            <el-input
              v-if="col.component === 'Input'"
              :type="col.type"
              :maxlength="col.maxlength"
              :show-word-limit="col.showWordLimit"
              v-model="blockSceneForm[col.key]"
            />
          </el-form-item>
        </el-col>
      </el-form>
      <div style="text-align: end">
        <el-button @click="showBlockSceneDialog = false">取消</el-button>
        <el-button type="primary" @click="submitBlockScene(blockSceneRef)"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import router from "@/router/index";
import {
  queryByPage,
  createOpinionOrder,
  createOriginData,
  getSceneIdBySceneName,
  getTestOwnerByappName,
  updateClusterScene,
  fetchBlockScene,
  fetchSceneNameAutocomplete,
} from "./scenceApi";
import {
  CaretBottom,
  CaretTop,
  QuestionFilled,
  Filter,
} from "@element-plus/icons-vue";
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, onMounted, computed, onActivated, watch } from "vue";
import searchArea from "@/components/issuesOpinionManage/commonArea/serachArea.vue";
import { BLOCK_FORM_COL, defaulList, otherList } from "./index";
import {
  defaultFormData,
  cPublicProblemFormComponentsData,
  sourceFormComponentsData,
  allColumn,
  defaultColumn,
  defaultProductCheckAll,
  defaultProductIndeterminate,
  SELECT_ALL,
  editSourceSceneDisabledKeys,
} from "./index";
import {
  PRIORTY_DEFAULT_VALUE,
  productMap,
} from "../../../components/issuesOpinionManage/commonArea/checkList";
import { cloneDeep } from "lodash-es";
import { useUserStore } from "@/store/modules/user";
import { login } from "@/api/system/user";
import {
  string2Array,
  transTime,
} from "@/components/issuesOpinionManage/commonArea/commonMethods";
import ColumnSettingDialog from "@/components/columnSettingsDialog/index.vue";
import { LocalStorageEnum } from "@/components/columnSettingsDialog/index";
import { storage } from "@/utils/Storage";
import { csrfTokenManager } from "@/utils/CSRFTokenManager";
import { isDev, WO_AUTH } from "@/utils/env";
import SourceSceneEditForm from "@/views/sourceScene/sourceSceneEditForm/index.vue";
import { MultipleKeys, getDisableKeys } from "../sourceSceneEditForm/index";
import { pageSizes, TABLE_EMPTY_TXT } from "@/utils/constant";

const productTypeList = computed(() => {
  return Object.keys(productMap);
});
const productModelChange = () => {
  filterObj.value.productModel = productModelList.value;
  queryData();
};
const productTypeChange = () => {
  filterObj.value.productModel = [];
  productModelList.value = [];
  queryData();
};
// 所有产品机型列表
const allProductModelList = productTypeList.value.reduce((pre, cur) => {
  return pre.concat(productMap[cur]);
}, []);
// 产品类型change时，清空产品机型
const selectChange = (key, createOrImport) => {
  // key为productType清空机型，createOrImport表示新增或导入清空不同的弹框的productModel
  if (key === "productType") {
    createOrImport === "import"
      ? (reactive(sourceFormData).productModel = [])
      : (reactive(cPublicProblemFormData).productModel = []);
    // 产品机型
    importProductModelList.value = [];
    if (createOrImport === "import") {
      sourceFormData.productType.forEach((v) => {
        if (productMap[v]) {
          importProductModelList.value.push(...productMap[v]);
        }
      });
      sourceFormData.productType = sourceFormData.productType.filter(
        (v) => v !== SELECT_ALL
      );
    } else {
      cPublicProblemFormData.productType.forEach((v) => {
        if (productMap[v]) {
          importProductModelList.value.push(...productMap[v]);
        }
      });
      cPublicProblemFormData.productType =
        cPublicProblemFormData.productType.filter((v) => v !== SELECT_ALL);
    }
  }
  if (key === "productModel") {
    if (createOrImport === "import") {
      sourceFormData.productModel = sourceFormData.productModel.filter(
        (v) => v !== SELECT_ALL
      );
    } else {
      cPublicProblemFormData.productModel =
        cPublicProblemFormData.productModel.filter((v) => v !== SELECT_ALL);
    }
  }
  // 设置样式
  if (createOrImport === "import") {
    setSelectStyle(sourceFormData);
  } else {
    setSelectStyle(cPublicProblemFormData);
  }
};
const productModelList = ref([]);
const sortField = ref(["totalVolume"]);
const sortOrder = ref(["descending"]);
const exportLoading = ref(false);
const userStore = useUserStore();
const userInfo = userStore;
console.log("roleList", userInfo);

// 通用权限检查函数
const checkResourcePermission = (resourceId: string) => {
  if (!resourceId || !userStore.menuList) return false;
  // 检查菜单列表中是否包含指定资源ID
  return userStore.menuList.some((menu) => menu.resourceId === resourceId);
};

// 判断是否有导入源声权限
const hasImportPermission = computed(() => {
  return checkResourcePermission("ImportSourceSound");
});

// 判断是否创建问题权限
const hasListSettingPermission = computed(() => {
  return checkResourcePermission("CreatingPublicOpinionQuestion");
});
// 判断是否有导出权限
const hasExportPermission = computed(() => {
  return checkResourcePermission("ExportPublicOpinion");
});
// 判断是否有编辑权限
const hasEditPermission = computed(() => {
  return checkResourcePermission("ModifySourceScene");
});

// 判断是否有屏蔽权限
const hasBlockScenePermission = computed(() => {
  return checkResourcePermission("BlockScene");
});

const getDefaultFilterOptionVals = () => {
  const res = {};
  defaulList.concat(otherList).forEach((item) => {
    res[item.key] = ["all"];
  });
  return res;
};

const filterOptions = ref(getDefaultFilterOptionVals());
const loading = ref(false);
const handleFilterOptions = (value) => {
  filterOptions.value = value;
  queryData();
};
const getFilterParams = () => {
  const res = {};
  Object.keys(filterOptions.value).forEach((key) => {
    if (!filterOptions.value[key].includes("all")) {
      res[key] = filterOptions.value[key];
    }
  });
  Object.keys(filterObj.value).forEach((key) => {
    if (key === "createTime") {
      res.startTime = filterObj.value[key]?.length
        ? filterObj.value.createTime[0]
        : "";
      res.endTime = filterObj.value[key]?.length
        ? filterObj.value.createTime[1]
        : "";
    } else {
      res[key] = filterObj.value[key];
    }
  });
  return res;
};

const pageInfo = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});
const tableData = ref([]);
const manageTable = ref("");
const columnList = ref([]);
const setColumn = (columnData) => {
  columnList.value = [
    { prop: "checkbox", label: "", width: "40px", type: "selection" },
    ...cloneDeep(columnData),
  ];
};
onMounted(() => {
  queryData();
  // 设置动态列
  const localStorageClounm = storage.get(LocalStorageEnum.SOURCE_SCENE_MANAGE);
  if (localStorageClounm) {
    setColumn(localStorageClounm);
  } else {
    setColumn(defaultColumn);
  }
});
const gotoDetail = (data) => {
  router.push({
    path: "/source-scene-detail",
    query: { sceneId: data.sceneId, sceneName: data.sceneName },
  });
};
const jumpIssueDetail = (data) => {
  router.push({
    path: "/opinion-management-detail",
    query: { id: data.opinionIssueId },
  });
};
const showCPublicProblem = ref(false);
let cPublicProblemFormData = reactive({});

const setDisposePerson = async () => {
  // 查询指定处理人
  const result = await getTestOwnerByappName({
    appName: cPublicProblemFormData.appName,
  });
  const disposePersonOptions = [];
  result?.testOwnerList.forEach((tester) => {
    disposePersonOptions.push({
      label: tester,
      value: tester,
    });
  });
  for (let i = 0; i < cPublicProblemFormComponentsData.length; i++) {
    if (cPublicProblemFormComponentsData[i].key === "disposePerson") {
      cPublicProblemFormComponentsData[i].option = disposePersonOptions;
      break;
    }
  }
  cPublicProblemFormData.disposePerson = result.testOwnerList[0]; // 返回的第一个就是默认处理人
};

const createProblem = async (data) => {
  if (!hasListSettingPermission.value) {
    return;
  }
  const {
    productModel = "",
    productType = "",
    packageName = "",
    source = "",
    channelSource = "",
  } = data;
  cPublicProblemFormData = reactive(
    cloneDeep({
      ...data,
      appPackageName: packageName || data.appPackageName,
      automated: data.automated == true ? "自动" : "人工",
      firstReportTime: data.firstReportTime?.slice(0, 10),
      clusterTime: data.clusterTime?.slice(0, 10),
      createTime: data.createTime?.slice(0, 10),
      suggestResolveTime: data.suggestResolveTime?.slice(0, 10),
      productModel: string2Array(productModel, ","),
      productType: string2Array(productType, ","),
      channelSource: string2Array(source || channelSource, ","),
      sourceFormData: data.sourceFormData,
      priority: PRIORTY_DEFAULT_VALUE, // 优先级默认回填 高
      reportedPerson: userInfo.displayNameCn,
    })
  );
  // 设置选中筛选项
  cPublicProblemFormData.productType.forEach((v) => {
    productMap[v] && importProductModelList.value.push(...productMap[v]);
  });
  setSelectStyle(cPublicProblemFormData); // 设置样式
  try {
    await setDisposePerson();
  } catch (e) {
    console.error(`获取指定处理人失败`, e);
  }
  showCPublicProblem.value = true;
  formRef.value?.clearValidate();
};
const filterObj = ref({
  appName: "",
  sceneName: "",
  createTime: "",
  productType: "",
  productModel: [],
});
const showAllInputFilter = ref(false);
const reasetSearch = () => {
  pageInfo.value.pageNo = 1;
  pageInfo.value.pageSize = 10;
  Object.keys(filterObj.value).forEach((key) => {
    filterObj.value[key] = "";
  });
  filterObj.value.productModel = [];
  productModelList.value = [];
  // 排序重置
  sortField.value = ["totalVolume"];
  sortOrder.value = ["descending"];
  manageTable.value.clearSort(); // 前端页面排序去除
  filterOptions.value = getDefaultFilterOptionVals();
  queryData();
};
const handleSizeChange = (num) => {
  pageInfo.value.pageNo = 1;
  pageInfo.value.pageSize = num;
  queryData();
};
const handleCurrentChange = (num) => {
  pageInfo.value.pageNo = num;
  queryData();
};
// 排序
const sortChange = (e) => {
  const index = sortField.value.indexOf(e.prop);
  if (index >= 0) {
    if (e.order) {
      sortField.value[index] = e.prop;
      sortOrder.value[index] = e.order;
    } else {
      sortField.value.splice(index, 1);
      sortOrder.value.splice(index, 1);
    }
  } else {
    sortField.value.push(e.prop);
    sortOrder.value.push(e.order);
  }
  queryData();
};

const setHeaderClass = (params: any) => {
  const index = sortField.value.indexOf(params.column.property);
  if (index >= 0) {
    params.column.order = sortOrder.value[index];
  }
};

// 获取已有数据
const queryData = async () => {
  loading.value = true;
  const param = {
    pageNo: pageInfo.value.pageNo,
    pageSize: pageInfo.value.pageSize,
    sortField: sortField.value || "",
    sortOrder: sortOrder.value || "",
    ...getFilterParams(),
  };
  try {
    const res = await queryByPage(param);
    tableData.value = res.list;
    pageInfo.value.total = res.total || 0;
    setTimeout(() => {
      loading.value = false;
    });
  } catch (error) {
    console.error("获取数据失败:", error);
    loading.value = false;
  }
};

const showImportSource = ref(false);
const transAuto = (val) => {
  return val === true ? "自动" : "人工";
};

// 导入源声formRef
const originVolumeformRef = ref<FormInstance>();
// 创建问题formRef
const formRef = ref<FormInstance>();
let sourceFormData = reactive(
  cloneDeep({
    ...defaultFormData,
    automated: defaultFormData.automated == true ? "自动" : "人工",
    firstReportTime: defaultFormData.firstReportTime?.slice(0, 10),
    clusterTime: defaultFormData.clusterTime?.slice(0, 10),
    createTime: defaultFormData.createTime?.slice(0, 10),
    suggestResolveTime: defaultFormData.suggestResolveTime?.slice(0, 10),
  })
);
// 下拉筛选，自定义头部全选
let productCheckAll = reactive(cloneDeep(defaultProductCheckAll));
let productIndeterminate = reactive(cloneDeep(defaultProductIndeterminate));
// 产品机型下拉框
let importProductModelList = ref([]);
const checkAllChangeHandle = (val, key, formData) => {
  productIndeterminate[key] = false;
  if (val) {
    if (key === "productType") {
      formData.productType = [SELECT_ALL];
      importProductModelList.value = cloneDeep(allProductModelList);
    }
    if (key === "productModel") {
      formData.productModel = [SELECT_ALL];
    }
  } else {
    if (key === "productType") {
      formData.productType = [];
      importProductModelList.value = [];
    }
    if (key === "productModel") {
      formData.productModel = [];
    }
  }
};
/**
 * 设置全选样式
 * @param formData 弹窗数据
 */
const setSelectStyle = (formData) => {
  if (formData.productType.length === 0) {
    productCheckAll.productType = false;
    productIndeterminate.productType = false;
  } else if (formData.productType.length === allProductModelList.length) {
    productCheckAll.productType = true;
    productIndeterminate.productType = false;
  } else {
    productIndeterminate.productType = true;
  }
  if (formData.productModel.length === 0) {
    productCheckAll.productModel = false;
    productIndeterminate.productModel = false;
  } else if (
    formData.productModel.length === importProductModelList.value.length
  ) {
    productCheckAll.productModel = true;
    productIndeterminate.productModel = false;
  } else {
    productIndeterminate.productModel = true;
  }
};
/**
 * 打开导入源声弹窗
 * @param needCleanValues 是否重置表单数据
 */
const importSource = (needCleanValues: boolean) => {
  showCPublicProblem.value = false; // 关闭创建问题弹窗
  importSourceBtn.value.loading = false;
  importSourceBtn.value.txt = "确定";
  if (needCleanValues) {
    sourceFormData = reactive(cloneDeep(defaultFormData));
    disableCreateIssueRadio(false);
    getSceneIdParams.sceneName = "";
    getSceneIdParams.originVolume = 0;
    getSceneIdParams.appName = "";
    importProductModelList.value = [];
    productCheckAll = reactive(cloneDeep(defaultProductCheckAll));
    productIndeterminate = reactive(cloneDeep(defaultProductIndeterminate));
  }
  showNextBtn.value = sourceFormData.createOpinionIssue;
  sourceFormData.reportedPerson = userInfo.displayNameCn;
  showImportSource.value = true;
  setTimeout(() => {
    originVolumeformRef.value?.clearValidate();
  });
};

let needInvokeImportOrigin = false;
const importSourceSubmit = (action) => {
  if (!originVolumeformRef.value) return;
  originVolumeformRef.value.validate(async (valid, fields) => {
    if (valid) {
      if (action === "next") {
        showPreBtn.value = true;
        needInvokeImportOrigin = true;
        createProblem(sourceFormData);
      } else {
        showPreBtn.value = false;
        // 调用导入源声接口
        handleImport();
      }
      showImportSource.value = false;
    } else {
      console.log("error submit!", fields);
    }
  });
};

// 导入源声
const handleImport = async () => {
  const param = reactive(sourceFormData);
  if (Array.isArray(param.productModel)) {
    param.productModel = param.productModel.join(",");
  }
  if (Array.isArray(param.productType)) {
    param.productType = param.productType.join(",");
  }
  if (Array.isArray(param.source)) {
    param.source = param.source.join(",");
  }
  try {
    console.log(`导入源声参数 `, JSON.stringify(param));
    param.volume = Number(param.volume);
    await createOriginData(param);
    showImportSource.value = false;
    queryData();
    ElMessage({
      type: "success",
      message: "导入数据成功！",
    });

    // 清空导入源声弹窗里数据
    sourceFormData = reactive(cloneDeep(defaultFormData));
    return true;
  } catch (error) {
    console.error("导入数据失败:", error);
    error.msg && ElMessage.error(error.msg);
    loading.value = false;
    throw error;
  }
};
const createProblemSubmit = () => {
  if (!formRef.value) return;
  formRef.value.validate(async (valid, fields) => {
    if (valid) {
      if (needInvokeImportOrigin) {
        // 调用导入源声接口
        await handleImport();
        needInvokeImportOrigin = false;
        handleCreate();
      } else {
        handleCreate();
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const handleCreate = async () => {
  const param = reactive(cPublicProblemFormData);
  if (Array.isArray(param.productModel)) {
    param.productModel = param.productModel.join(",");
  }
  if (Array.isArray(param.productType)) {
    param.productType = param.productType.join(",");
  }
  if (Array.isArray(param.channelSource)) {
    param.channelSource = param.channelSource.join(",");
  }
  console.log(`创建问题参数 `, JSON.stringify(param));
  try {
    // 创建问题
    const res = await createOpinionOrder(param);
    console.log("res", res);
    showCPublicProblem.value = false;
    queryData();
    ElMessage({
      type: "success",
      message: "新增数据成功！",
    });
  } catch (error) {
    console.error("新增数据失败:", error);
    ElMessage({
      type: "error",
      message: error.msg || "新增数据失败!",
    });
    loading.value = false;
  }
};

const showNextBtn = ref(false);
const showPreBtn = ref(false);
const inputValueChange = async (item, value) => {
  if (item.key === "sceneName") {
    getSceneIdParams[item.key] = value;
    await getTotalVolume();
  } else if (item.key === "volume") {
    getSceneIdParams["originVolume"] = value;
    await getTotalVolume();
  } else if (item.key === "appName") {
    getSceneIdParams.appName = value;
    await getTotalVolume();
  } else if (item.key === "createOpinionIssue") {
    // 导入问题源声时，若选择要创建问题，提交按钮变成下一步按钮
    if (value) {
      showNextBtn.value = true;
      importSourceBtn.value.txt = "下一步";
    } else {
      showNextBtn.value = false;
      importSourceBtn.value.txt = "确定";
    }
  }
};
const handleExportRes = () => {
  return new Promise((resolve, reject) => {
    const req = new XMLHttpRequest();
    exportLoading.value = true;
    const queryParams = {
      pageNo: 1,
      pageSize: 99999,
      sortField: sortField.value || "",
      sortOrder: sortOrder.value || "",
      ...getFilterParams(),
    };
    req.open(
      "POST",
      `${window.location.origin}${
        import.meta.env.VITE_APP_BASE_API
      }/clusterScene/exportData`,
      true
    );
    req.responseType = "blob";
    req.setRequestHeader("Content-Type", "application/json");
    const csrfToken = csrfTokenManager.getToken();
    if (csrfToken) {
      req.setRequestHeader("X-CSRF-TOKEN", csrfToken);
    }
    if (isDev()) {
      req.setRequestHeader("token", atob(WO_AUTH));
    }
    req.onload = function () {
      const data = req.response;
      if (req.status === 413) {
        exportLoading.value = false;
        ElMessage.error("导出数据量过大，限制50000条。请修改查询条件后重试");
        reject();
        return;
      }

      if (data.size === 0) {
        exportLoading.value = false;
        ElMessage.error("没有数据可导出");
        reject();
        return;
      }
      const blob = new Blob([data]);
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.download = "源声场景管理导出.xlsx";
      a.href = blobUrl;
      a.click();
      exportLoading.value = false;
      resolve(true);
    };
    req.send(JSON.stringify(queryParams));
  });
};

const getSceneIdParams = {
  sceneName: "",
  appName: "",
  originVolume: 0,
};
const importSourceBtn = ref({
  loading: false,
  txt: "确定",
});
const getTotalVolume = async () => {
  if (
    !getSceneIdParams.sceneName ||
    Number(getSceneIdParams.originVolume) <= 0 ||
    !getSceneIdParams.appName
  ) {
    return;
  }
  // 创建问题时，当输入场景名称后，需要后端接口返回一个场景分类编码
  // 获取场景分类编码
  let result;
  if (!/^[1-9]\d*$/.test(getSceneIdParams.originVolume.toString())) {
    return;
  }
  if (
    getSceneIdParams.originVolume <= 0 ||
    getSceneIdParams.originVolume > 2147483647
  ) {
    return;
  }
  importSourceBtn.value.loading = true;
  importSourceBtn.value.txt = "获取场景Id中";
  result = await getSceneIdBySceneName(getSceneIdParams);
  if (result?.opinionIssueId) {
    // 若该源声已有对应的问题id，则不能再创建舆情
    showNextBtn.value = false;
    sourceFormData.createOpinionIssue = false;
    disableCreateIssueRadio(true);
    ElMessage.info(`该场景已存在问题`);
  } else {
    disableCreateIssueRadio(false);
  }
  sourceFormData.sceneId = result.sceneId;
  sourceFormData.totalVolume = result.totalVolume;
  sourceFormData.clusterTime = result.clusterTime;
  importSourceBtn.value.loading = false;
  importSourceBtn.value.txt = sourceFormData.createOpinionIssue
    ? "下一步"
    : "确定";
};

const sourceData = ref(sourceFormComponentsData);
const disableCreateIssueRadio = (enable) => {
  for (let i = sourceData.value.length - 1; i >= 0; i--) {
    const item = sourceData.value[i];
    if (item.key === "createOpinionIssue") {
      item.disabled = enable;
      break;
    }
  }
};
onActivated(() => {
  queryData();
});

// ------------------- 编辑场景 ----------------------
const showEditSource = ref(false);
const editSourceFormData = ref({});
const disabledKeys = ref([]);
/**
 * 点击编辑
 * @param rowData 行内数据
 */
const editSourceData = (rowData) => {
  if (rowData.automated) return;
  showEditSource.value = true;
  // 单独将表单中的字段置空
  Object.keys(editSourceFormData.value).forEach((key) => {
    editSourceFormData.value[key] = "";
  });
  Object.keys(rowData).forEach((key) => {
    if (MultipleKeys.includes(key)) {
      editSourceFormData.value[key] = rowData[key]
        ? rowData[key].split(",")
        : [];
    } else {
      editSourceFormData.value[key] = rowData[key];
    }
  });
  editSourceFormData.value.volume = rowData.totalVolume; // 源声声量
  disabledKeys.value = getDisableKeys(
    editSourceSceneDisabledKeys,
    editSourceFormData.value
  );
};
/**
 * 提交编辑
 * @param formData 表单数据
 */
const editSourceSubmitHandle = async (formData) => {
  try {
    console.log("编辑数据", formData);
    await updateClusterScene(formData);
    ElMessage({
      type: "success",
      message: "编辑成功",
    });
    queryData();
    showEditSource.value = false;
  } catch (error) {
    ElMessage({
      type: "error",
      message: error.msg || "数据更新失败!",
    });
  } finally {
    loading.value = false;
  }
};
/**
 * 取消编辑
 * @param formData 表单数据
 */
const editSourceCancelHandle = (formData) => {
  showEditSource.value = false;
};

// 屏蔽场景表单列属性
const blockFromCol = ref(BLOCK_FORM_COL);

// 屏蔽场景弹窗
const showBlockSceneDialog = ref(false);
// 场景屏蔽表单ref
const blockSceneRef = ref<FormInstance>();

// 屏蔽表单数据
const blockSceneForm = reactive({
  sceneId: "",
  isBlock: true,
  blockReason: "",
  remark: "",
});

const handleBlockSceneDialog = (id) => {
  showBlockSceneDialog.value = true;
  blockSceneForm.sceneId = id;
};

// 屏蔽理由为其他时，说明必填！
watch(
  () => blockSceneForm.blockReason,
  (newVal) => {
    if (newVal === "其他") {
      blockFromCol.value[1].rules.required = true;
    } else {
      blockFromCol.value[1].rules.required = false;
    }
  }
);

// 提交屏蔽场景申请
const submitBlockScene = async (formEl) => {
  if (!formEl) {
    return;
  }
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      try {
        const data = {
          sceneId: blockSceneForm.sceneId,
          isBlock: true,
          blockReason: blockSceneForm.blockReason,
          remark: blockSceneForm.remark,
        };
        await fetchBlockScene(data);
        ElMessage.success("屏蔽成功");
        showBlockSceneDialog.value = false;
        queryData();
      } catch (e) {
        console.error(`屏蔽场景申请失败, `, e);
        ElMessage.error("屏蔽场景申请失败");
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const fetchSuggestions = (key, value, cb) => {
  if (key === "sceneName") {
    querySceneNameList(value, cb);
  }
};

// 导入源声弹窗，输入场景名称联想功能
const querySceneNameList = async (value, cb) => {
  try {
    if (!value) {
      return;
    }
    const data = {
      appName: sourceFormData.appName,
      keyword: value,
      limit: 10,
    };
    const res = await fetchSceneNameAutocomplete(data);
    const options = [];
    res?.sceneList.forEach((item) => {
      options.push({ value: item });
    });
    cb(options);
  } catch (e) {
    console.error("场景名称自动联想接口报错", e);
  }
};

// 清空单个查询条件
const resetSingle = (obj, key, newVal) => {
  if (obj[key] === newVal) {
    return;
  }
  obj[key] = newVal;
  queryData();
};
</script>

<style lang="less" scoped>
.opinion-manage {
  .mt-8 {
    margin-top: 8px;
  }

  .ml-8 {
    margin-left: 8px;
  }

  .ml-16 {
    margin-left: 16px;
  }

  .table-head {
    display: flex;
    justify-content: space-between;
  }

  .table-head-left {
    display: flex;
  }
  .disabled {
    color: #999;
    opacity: 0.5;
    cursor: not-allowed;
  }
}
.question-icon {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
  cursor: pointer;
}
</style>
