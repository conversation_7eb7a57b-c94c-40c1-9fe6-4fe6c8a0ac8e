#!/bin/bash

set -ex
set -o pipefail

BASE_PATH=$(cd $(dirname $0); pwd)
PROJECT_PATH=$(cd $BASE_PATH/..;pwd)

SERVICE_NAME=$(awk -F "=" '/service_name/ {print $2}' "${BASE_PATH}"/service.ini)
ORG_NAME=$(awk -F "=" '/DOCKER_ORG_NAME/ {print $2}' "${BASE_PATH}"/service.ini)
SCOPE_NAME=$(awk -F "=" '/SCOPE/ {print $2}' "${BASE_PATH}"/service.ini)
IMAGE_NAME=$(awk -F "=" '/IMAGE_NAME/ {print $2}' "${BASE_PATH}"/service.ini)
IMAGE_VERSION=
NUWA_PATH=$(awk -F "=" '/NUWA_PATH/ {print $2}' "${BASE_PATH}"/service.ini)
DOCKER_PACKAGE_DIR=PartnerDTSESupportService

function build_app_package() {
  #先调用build.sh编译构建，此时包里面只有vm版的软件包
  source ${PROJECT_PATH}/script/build_docker_vm.sh
  IMAGE_VERSION=${PACKAGE_VERSION}

  #build2.0参数IMAGE_TYPE指定arm则打arm镜像
  if [[ ${IMAGE_TYPE} != "" ]]; then
    IMAGE_VERSION="${IMAGE_TYPE}_${IMAGE_VERSION}"
  fi

  cd $PROJECT_PATH
  NUWA_VERSION=$(npm pkg get version|tr -d '"')
  NUWA_PATH=${NUWA_PATH}${NUWA_VERSION}
}

function build_unzip_package() {
  #软件包的具体路径
  PACKAGE_PATH=`ls ${PROJECT_PATH}/${SERVICE_NAME}*.zip`
  IMAGE_DIR_PATH="${PROJECT_PATH}/image"
  PACKAGE_DIR_PATH="${PROJECT_PATH}/package"

  mkdir -p ${IMAGE_DIR_PATH}
  mkdir -p ${PACKAGE_DIR_PATH}

  #将vm软件包解压到即将打包的容器目录下
  unzip -q ${PACKAGE_PATH} -d ${IMAGE_DIR_PATH}/${DOCKER_PACKAGE_DIR}

  cd ${IMAGE_DIR_PATH}/${DOCKER_PACKAGE_DIR}
  chmod 700 bin configtemplate
  chmod 500 bin/*
  chmod 600 configtemplate/*
}

function build_docker_image() {
  #获取Dockerfile文件
  cd ${IMAGE_DIR_PATH}
  cp ${PROJECT_PATH}/script/Dockerfile .

  #拉取nuwa基础镜像
  set +e
  docker pull ${NUWA_PATH}
  docker tag ${NUWA_PATH} nuwa:3.0
  set -e

  #基于nuwa基础镜像，构建业务镜像 （tag名引用基础镜像）
  docker build -t ${IMAGE_NAME}:${IMAGE_VERSION} --build-arg docker_image=nuwa:3.0 ./

  #基础镜像溯源使用
  echo "dockerfile=${PROJECT_PATH}/script/Dockerfile" >> ${WORKSPACE}/buildImage.properties
  echo "docker_var=${NUWA_PATH},nuwa:3.0" >> ${WORKSPACE}/buildImage.properties
  echo ${IMAGE_NAME}:${IMAGE_VERSION} build image successed

  #构建镜像信息存储到buildInfo.properties
  echo buildVersion=${PACKAGE_VERSION} > ${WORKSPACE}/buildInfo.properties
  echo imageName=${IMAGE_NAME}:${IMAGE_VERSION} >> ${WORKSPACE}/buildInfo.properties
  echo orgName=${ORG_NAME} >> ${WORKSPACE}/buildInfo.properties
  echo scopeName=${SCOPE_NAME} >> ${WORKSPACE}/buildInfo.properties
}

function rhino_start() {
  if [[ "${rhino_enable}" != "false" ]] && [[ -n "${rhino_agent_group}" ]] && [[ -n "${rhino_agent_app}" ]]; then
    DOCKER_PACKAGE_DIR_RHINO=${DOCKER_PACKAGE_DIR}

    if [[ ! -n "${rhino_agent_base_dir}" ]]; then
      rhino_agent_base_dir=/opt/huawei/rhino-iast
    fi
    DOWNLOAD_COMMAND='\n\ncurl -X GET "https://rhino-iast-open.hwcloudtest.cn/api/v1/agent/download" -H "accept: */*" --output /opt/huawei/rhino-agent.jar'
    AGENT_COMMAND='\n\nJAVA_OPTS="$JAVA_OPTS -javaagent:/opt/huawei/rhino-agent.jar=agent_group='${rhino_agent_group}'&agent_app='${rhino_agent_app}'&agent_base_dir='${rhino_agent_base_dir}'"'
    if [[ ! -d "${DOCKER_PACKAGE_DIR_RHINO}/service/bin" ]]; then
      mkdir -m 700 -p ${DOCKER_PACKAGE_DIR_RHINO}/service/bin
      ls ${DOCKER_PACKAGE_DIR_RHINO}
    fi
    echo -e $DOWNLOAD_COMMAND >> ${DOCKER_PACKAGE_DIR_RHINO}/service/bin/startup_service.sh
    echo -e $AGENT_COMMAND >> ${DOCKER_PACKAGE_DIR_RHINO}/service/bin/startup_service.sh
    cat ${DOCKER_PACKAGE_DIR_RHINO}/service/bin/startup_service.sh
  fi
}

function run_rhino_start() {
  #执行IAST脚本,仅在snap包中执行
  if [[ ${releaseVersion} == "" ]]; then
    rhino_agent_group=PartnerTechSupportService
    rhino_agent_app=PartnerSupportService
    rhino_start
  fi
}

function main() {
  build_app_package
  build_unzip_package
  build_docker_image
}

main
