import { priorityOption } from "@/components/issuesOpinionManage/commonArea/checkList";

export const EditComponentsData = {
  priority: {
    label: "优先级",
    prop: "priority",
    component: "Select",
    option: priorityOption,
  },
  remark: {
    label: "备注",
    prop: "remark",
    component: "Input",
    type: "textarea",
    rules: {
      validator(rule, value, callback) {
        if (value && value.length > 255) {
          return new Error("长度不能超过255！");
        }
        return true;
      },
    },
  },
  levelFunction: {
    label: "L3/6功能",
    prop: "levelFunction",
    component: "Input",
    rules: {
      validator(rule, value, callback) {
        if (value && value.length > 255) {
          return new Error("长度不能超过255！");
        }
        return true;
      },
    },
  },
  responsibleTeam: {
    label: "责任团队/TL",
    prop: "responsibleTeam",
    component: "Input",
    rules: {
      validator(rule, value, callback) {
        if (value && value.length > 255) {
          return new Error("长度不能超过255！");
        }
        return true;
      },
    },
  },
  owner: {
    label: "责任人",
    prop: "owner",
    component: "Input",
    rules: {
      validator(rule, value, callback) {
        if (value && value.length > 255) {
          return new Error("长度不能超过255！");
        }
        return true;
      },
    },
  },
};

export const CanEditKey = Object.keys(EditComponentsData);
