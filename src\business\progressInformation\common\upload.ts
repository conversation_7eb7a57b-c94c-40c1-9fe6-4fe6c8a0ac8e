import { ewpService as service } from '@/utils/axios';

export const getAuthCode = async (params) => {
  try {
    const response = await service.post(`/fileUpload/authCode`, params, {
        headers: {
            'Content-Type': 'application/json',
        },
        timeout: 500000
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export const upload = async (params) => {
  try {
    const response = await service.post('/fileUpload/attachment', params, {
      headers: {
          'Content-Type': 'multipart/form-data',
      },
  });
    return response;
  } catch (error) {
    throw error;
  }
}