<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <h1>404</h1>
      <h2>页面未找到</h2>
      <p>抱歉，您访问的页面不存在</p>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style scoped lang="scss">
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
  
  .not-found-content {
    text-align: center;
    
    h1 {
      font-size: 72px;
      color: #1890ff;
      margin-bottom: 10px;
    }
    
    h2 {
      font-size: 24px;
      color: #333;
      margin-bottom: 20px;
    }
    
    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 30px;
    }
  }
}
</style> 
