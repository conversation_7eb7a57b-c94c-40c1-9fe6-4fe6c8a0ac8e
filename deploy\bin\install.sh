#!/bin/sh

#安装脚本路径
CURRENT_DIR=$(
  cd $(dirname $0)
  pwd
)

PROJECT_ROOT_DIR=$(dirname $(pwd))

#定义LOG
SYS_LOG="$CURRENT_DIR/install.log"
echo "Begin:" >$SYS_LOG
log_info() {
  local date=$(date)
  local para=$1
  echo "log info:$date $1" >>$SYS_LOG
}

log_err() {
  local date=$(date)
  local para=$1
  echo "log err:$date $1" >>$SYS_LOG
}

log_warn() {
  local date=$(date)
  local para=$1
  echo "log warn:$date $1" >>$SYS_LOG
}

#安装包的基本路径
BASE_DIR="$CURRENT_DIR/../"
log_info "BASE_DIR:$BASE_DIR"

#本机IP
CURRENT_IP=$(ip route get 1 | awk '{print $7;exit}')
log_info "CURRENT_IP:$CURRENT_IP"

#网关配置
updateApiGWConf() {
  log_info "Begin to do updateApiGWConf!"
  if [ "x$APIGW_HOME" == "x" ] || [ ! -d $APIGW_HOME ]; then
    export APIGW_HOME=/opt/huawei/apps/alliance/apigateway-service/apigateway-service
    log_warn "Varible APIGW_HOME is not exist!"
  fi

  ApiGWConf_Dir=$BASE_DIR/apigw_conf/
  ApiGWConf_Dest=$APIGW_HOME/webapps/ROOT/WEB-INF/
  log_info "ApiGWConf_Dir : $ApiGWConf_Dir"
  log_info "ApiGWConf_Dest : $ApiGWConf_Dest"

  rm -rf "${ApiGWConf_Dest}"/swagger "${ApiGWConf_Dest}"/stub
  cp -r "${ApiGWConf_Dir}"/* "${ApiGWConf_Dest}"
  if [ $? == "0" ]; then
    log_info "Success to do updateApiGWConf!"
  else
    log_err "Fail to do updateApiGWConf!"
    exit 1
  fi
  log_info "End to do updateApiGWConf."

  log_info "Begin to restart ApiGateway."
  cd "${APIGW_HOME}"/bin && bash stop.sh
  log_info "End to restart ApiGateway."
}

setHostInfo() {
  log_info "Begin to do set host info"
  sed -i 's/127.0.0.1/'"$CURRENT_IP"'/' "${BASE_DIR}"/service/config/host.config.properties
  log_info "End to do set host info"
}

setStartUpParam() {
  log_info "Begin to do set setStartUpParam"
  tmpDir=${PROJECT_ROOT_DIR}/tmp
  mkdir -p ${tmpDir}
  to_replace_char="-Dnuwa.home="
  replace_char="-Djava.io.tmpdir=$tmpDir -Dnuwa.home="
  sed -i "s?${to_replace_char}?${replace_char}?gi" "${BASE_DIR}"/nuwa/bin/startup.sh
  log_info "End to do set setStartUpParam"
}

function chmod4Service
{
  cd $BASE_DIR
  find -type d -exec chmod -R 700 {} \;
  find -type f -exec chmod -R 600 {} \;
  chmod 500 $BASE_DIR/bin/*.sh
  chmod 500 $BASE_DIR/nuwa/bin/*.sh
  chmod 500 $BASE_DIR/nuwa/init.d/**/*.sh
  chmod 500 $BASE_DIR/nuwa/watchdog/bin/*.sh
  chmod 500 $BASE_DIR/nuwa/watchdog/tools/*.sh
}

log_info "Begin to do install!"
#updateApiGWConf
setHostInfo
setStartUpParam
chmod4Service
log_info "Success to do install!"
