<template>
  <div class="form-card">
    <div class="form-title">确认问题修复版本</div>
    <div class="description">修复信息</div>
    <el-form
      ref="topFormRef"
      :inline="true"
      :model="topFormData"
      :rules="topFromRules"
      label-width="95"
      label-position="top"
      style="margin-top: 24px"
      :disabled="!canEdit"
    >
      <el-form-item label="答复口径" class="form-item" prop="weknowId">
        <el-input
          placeholder="请输入"
          v-model="topFormData.weknowId"
          style="width: 368px"
          @blur="topFormData.weknowId = topFormData.weknowId.trim()"
        />
      </el-form-item>
    </el-form>
    <el-table class="table" :data="tableDataShow" style="width: 100%">
      <el-table-column prop="sort" :label="'序号'" width="88" />
      <el-table-column
        v-for="item in tableColumns"
        :key="item.key"
        :prop="item.key"
        :label="item.label"
        :class-name="item.key === 'relatedOrderId' ? 'order-id' : ''"
        :width="item.key === 'orderStatus' ? 120 : ''"
      >
        <template #header>
          <span><span class="required-field">*</span>{{ item.label }}</span>
        </template>
        <template #default="scope">
          <el-select
            v-if="item.type === 'select'"
            v-model="scope.row[item.key]"
            placeholder="请选择内容"
            :disabled="true"
          >
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-input
            v-if="item.type === 'input' && item.key !== 'relatedOrderId'"
            :placeholder="item.placeholder || '请输入内容'"
            v-model="scope.row[item.key]"
            :disabled="item.key === 'relatedOrderId' || !canEdit"
            maxlength="100"
            @blur="scope.row[item.key] = scope.row[item.key].trim()"
          />
          <template v-if="item.key === 'relatedOrderId'">
            <div
              class="id-text"
              @click="goRelatedOrder(scope.row, item.key, 'relatedOrderType')"
              :class="{ 'common-active-text': scope.row[item.key] }"
            >
              {{ scope.row[item.key] || "---" }}
            </div>
          </template>
          <div v-if="item.type === 'text'">
            {{ scope.row[item.key] || "---" }}
          </div>
          <el-date-picker
            v-if="item.type === 'datePicker'"
            v-model="scope.row[item.key]"
            placeholder="请选择时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="item.disabledDate"
            :disabled="!canEdit"
          />
        </template>
      </el-table-column>
    </el-table>
    <Upload
      ref="uploadRef"
      :uploadFileParams="uploadFileParams"
      :canEdit="canEdit"
      style="margin-top: 20px"
    ></Upload>
    <div class="description" v-if="!isNodeFinished && canEdit">操作信息</div>
    <el-form
      ref="formRef"
      :inline="true"
      :model="assignmentFormData"
      :rules="rules"
      label-width="95"
      label-position="top"
      style="margin-top: 24px"
      v-if="!isNodeFinished && canEdit"
    >
      <el-row style="width: 100%">
        <el-col :span="6">
          <el-form-item
            label="操作类型"
            prop="operationType"
            style="width: 100%; margin-bottom: 16px"
          >
            <el-radio-group
              v-model="assignmentFormData.operationType"
              @change="changeOperationType"
            >
              <el-radio :value="operationTypeOpt[1].value">{{
                operationTypeOpt[1].label
              }}</el-radio>
              <el-radio :value="operationTypeOpt[0].value">{{
                operationTypeOpt[0].label
              }}</el-radio>
              <el-radio :value="operationTypeOpt[2].value">{{
                operationTypeOpt[2].label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="assignmentFormData.operationType !== '0002'">
          <el-form-item style="width: 100%" label="指定处理人" prop="handler">
            <el-select
              placeholder="请选择内容"
              v-model="assignmentFormData.handler"
              filterable
            >
              <el-option
                v-for="item in handlerOptions"
                :label="item.label"
                :key="item.value"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item
        :label="
          assignmentFormData.operationType === '0000' ? '转单原因' : '驳回原因'
        "
        :label-position="'top'"
        v-if="
          assignmentFormData.operationType === '0000' ||
          assignmentFormData.operationType === '0002'
        "
        style="width: 100%"
        prop="reason"
      >
        <el-input
          type="textarea"
          :placeholder="
            assignmentFormData.operationType === '0000'
              ? '请输入转单原因'
              : '请输入驳回原因'
          "
          v-model="assignmentFormData.reason"
          show-word-limit
          maxlength="2000"
          @blur="assignmentFormData.reason = assignmentFormData.reason.trim()"
        />
      </el-form-item>
    </el-form>
    <div v-if="canEdit" class="form-button text-center">
      <el-button class="big-button" @click="onSave">{{
        isNodeFinished ? "更新信息" : "保存"
      }}</el-button>
      <el-button
        v-if="!isNodeFinished"
        class="big-button"
        type="primary"
        @click="assignmentSubmit"
        >提交</el-button
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, onMounted, computed, toRefs } from "vue";
import type { FormInstance } from "element-plus";
import { useRoute } from "vue-router";
import { ElMessage, ElLoading } from "element-plus";
import {
  fetchFixVersionTable,
  saveFixVersion,
  submitFixVersion,
} from "@/business/progressInformation/confirmFixVersion";
import { operationTypeOptions as operationTypeOpt } from "@/business/progressInformation/delimitProblem";
import { getHandlers } from "@/business/progressInformation/common/getHandlers";
import { weKnowIDReg } from "@/business/progressInformation/common/regex";
import { orderTypeOptions } from "@/business/progressInformation/relateOrder";
import { cloneDeep, isEqual } from "lodash-es";
import { validateTable } from "@/business/progressInformation/common/validateUtil";
import { goRelatedOrder } from "@/utils/opinionManagementDetail";

const emit = defineEmits(["refresh"]);

const refreshParentData = (step = null) => {
  emit("refresh", step);
};

const uploadRef = ref();

const uploadFileParams = ref([]);

const props = defineProps({
  node: {
    default: () => {},
    required: true,
  },
  canEdit: {
    default: () => false,
    required: true,
  },
});

const { node } = toRefs(props);
const isNodeFinished = node.value.isFinished;

const route = useRoute();
const opinionIssueId = route.query.id;
const formRef = ref<FormInstance>();
const topFormRef = ref<FormInstance>();

const checkWeknowId = (_rule, _value, callback) => {
  const value = topFormData.value.weknowId;
  if (!value || weKnowIDReg.test(value)) {
    callback && callback();
    return true;
  } else {
    callback &&
      callback(
        new Error("请输入正确的答复口径！如：zh-cn12345678、zh-cn-vol12345678")
      );
    return false;
  }
};

const topFromRules = {
  weknowId: [{ validator: checkWeknowId, trigger: "blur" }],
};

const handlerOptions = ref([]);

const rules = {
  // 指派
  operationType: [
    { required: true, message: "操作类型必填！", trigger: "blur" },
  ],
  handler: [{ required: true, message: "指定处理人必填！", trigger: "blur" }],
  reason: [{ required: true, message: "原因必填！", trigger: "blur" }],
};
// 提交状态
const showAssignment = ref(false);
const assignmentFormData = reactive({
  operationType: "",
  handler: "",
  reason: "",
});
const topFormData = ref({
  weknowId: "",
});

const assignmentSubmit = async () => {
  if (
    assignmentFormData.operationType === operationTypeOpt[0].value &&
    !assignmentFormData.handler
  ) {
    ElMessage({
      type: "error",
      message: "请输入指定处理人",
    });
    return;
  }
  if (
    (assignmentFormData.operationType === "0000" ||
      assignmentFormData.operationType === "0002") &&
    !assignmentFormData.reason
  ) {
    ElMessage({
      type: "error",
      message: "请填写原因",
    });
    return false;
  }
  const needCheckParams =
    !assignmentFormData.operationType ||
    assignmentFormData.operationType === operationTypeOpt[1].value;
  if (needCheckParams && !checkParams(true)) {
    return;
  }
  try {
    const params = getParams();
    await submitFixVersion(params);
    // 关闭对话框
    showAssignment.value = false;

    refreshParentData();
    // 显示成功提示
    ElMessage({
      type: "success",
      message: "提交成功",
    });
  } catch (err) {
    console.error("提交失败:");
    ElMessage({
      type: "error",
      message: "提交失败",
    });
  }
};

// 用户修改操作类型
const changeOperationType = async (operationType) => {
  assignmentFormData.reason = "";
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  let handlerList = null;
  if (opinionIssueId) {
    handlerList = await getHandlers(opinionIssueId, operationType);
  }
  if (handlerList) {
    handlerOptions.value = handlerList.handlers;
    assignmentFormData.handler = handlerList.defaultHandler;
  } else {
    ElMessage({
      type: "error",
      message: "获取下一步处理人失败",
    });
  }
  loading.close();
};

// 表格列
const tableColumns = [
  {
    label: "关联单类型",
    key: "relatedOrderType",
    type: "select",
    options: orderTypeOptions,
    disabled: true,
  },
  {
    label: "关联单号",
    key: "relatedOrderId",
    type: "input",
  },
  {
    label: "关联单状态",
    key: "orderStatus",
    type: "text",
  },
  {
    label: "问题修复版本",
    key: "appFixVersion",
    type: "input",
  },
  {
    label: "问题修复时间",
    key: "appFixTime",
    type: "datePicker",
    disabledDate: (time) => {
      return time.getTime() < Date.now() - 86400000; // 问题修复时间禁止选择今天以前的时间
    },
  },
];

// 修复版本表格数据
const fixVersionTable = ref([]);

const originData = ref({});

// 初始化时获取复现设备数据
onMounted(async () => {
  if (opinionIssueId) {
    const response = await fetchFixVersionTable(opinionIssueId);
    const tableData = response?.relateWorkOrders.map((item) => {
      return {
        sort: item.sort,
        relatedOrderType: item.relatedOrderType,
        relatedOrderId: item.relatedOrderId,
        appFixVersion: item.appResolveVersion,
        appFixTime: item.appResolveTime?.slice(0, 10).replaceAll("/", "-"),
        orderStatus: item.orderStatus,
      };
    });
    if (!tableData) {
      tableData.push({
        relatedOrderType: "",
        relatedOrderId: "",
        appFixVersion: "",
        appFixTime: "",
      });
    }
    fixVersionTable.value = tableData;
    topFormData.value.weknowId = response.weknowId;
    uploadFileParams.value = response.attachmentInfos;
    originData.value = cloneDeep({
      ...topFormData.value,
      tableDataShow: tableDataShow.value,
      uploadFile:
        response.attachmentInfos?.map((item) => {
          return {
            id: item.id,
            filePath: item.filePath,
          };
        }) || [],
    });
  }
});

// 添加索引
const tableDataShow = computed(() => {
  return fixVersionTable.value.map((item: any, index) => {
    return {
      ...item,
      index: index + 1,
    };
  });
});

// 校验表格是否填写正确
const checkTaableData = () => {
  const hasEmptyRow = tableDataShow.value.some((item) => {
    return (
      !item.appFixVersion ||
      !item.appFixTime ||
      !validateTable(item.appFixVersion)
    );
  });
  return !hasEmptyRow;
};

const checkParams = (needOperator) => {
  const isWeknowIDValid = checkWeknowId(null, undefined, null);
  if (!isWeknowIDValid) {
    ElMessage({
      type: "error",
      message: "请填写正确的知识ID",
    });
    return false;
  }
  const isTableValid = checkTaableData();
  if (!isTableValid) {
    ElMessage({
      type: "error",
      message: "请填写修复时间和修复版本",
    });
    return false;
  }
  if (needOperator) {
    const isOperationValid = checkOperation();
    if (!isOperationValid) {
      ElMessage({
        type: "error",
        message: "请填写操作类型和处理人",
      });
      return false;
    }
  }
  return true;
};

// 获取请求参数
const getParams = () => {
  return {
    opinionIssueId,
    weknowId: topFormData.value.weknowId,
    nextHandler: assignmentFormData.handler,
    operationType: assignmentFormData.operationType,
    reason: assignmentFormData.reason,
    attachmentItems: uploadRef.value.getFileListParams(),
    relateWorkOrders: tableDataShow.value.map((item) => {
      return {
        relatedOrderType: item.relatedOrderType,
        relatedOrderId: item.relatedOrderId,
        sort: item.sort,
        appResolveVersion: item.appFixVersion,
        appResolveTime: item.appFixTime?.slice(0, 10),
      };
    }),
  };
};

// 校验操作信息
const checkOperation = () => {
  if (!assignmentFormData.handler && !assignmentFormData.operationType) {
    return false;
  }
  return true;
};

// 保存
const onSave = async () => {
  if (!checkParams(false)) {
    return;
  }
  if (
    isNodeFinished &&
    isEqual(
      {
        ...topFormData.value,
        tableDataShow: tableDataShow.value,
        uploadFile: uploadRef.value.getFileListParams().map((item) => {
          return { id: item.id, filePath: item.filePath };
        }),
      },
      originData.value
    )
  ) {
    ElMessage({
      type: "error",
      message: "内容未修改,请修改内容后更新",
    });
    return;
  }
  try {
    const params = getParams();
    await (isNodeFinished ? submitFixVersion(params) : saveFixVersion(params));
    ElMessage({
      type: "success",
      message: isNodeFinished ? "更新成功" : "保存成功",
    });
    refreshParentData(node.value.step);
  } catch (err) {
    ElMessage({
      type: "error",
      message: isNodeFinished ? "更新失败" : "保存失败",
    });
  }
};
</script>
<style lang="scss" scoped>
@import "@/styles/common-styles.scss";
.el-form-item {
  margin: 0;
}

.form-card {
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  margin-top: 24px;

  .form-title {
    color: rgb(25, 25, 25);
    font-family: 鸿蒙黑体;
    font-size: 16px;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: 0%;
    text-align: left;
  }
  .el-table {
    border: 1px solid rgba(17, 26, 44, 0.1);
    border-radius: 8px;
    thead {
      th {
        background: rgb(248, 248, 248);
      }
      /* 添加必填项红色星号样式 */
      .required-field {
        color: #f56c6c;
        margin-right: 4px;
      }
    }
    :deep(.el-date-editor) {
      width: 100%;
    }
  }

  .form-box {
    margin-top: 24px;
  }

  .form-button {
    margin-top: 16px;
    .el-button + .el-button {
      margin-left: 24px;
    }
  }

  .table {
    margin-top: 16px;
  }
}

:deep(td.order-id) {
  .cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
