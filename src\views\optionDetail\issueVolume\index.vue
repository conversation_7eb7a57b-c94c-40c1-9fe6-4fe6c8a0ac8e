<template>
  <div class="issue-volume-container">
    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <div class="filter-left">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
          @change="handleDateChange"
          style="width: 280px"
          :max-date="new Date()"
        />
      </div>
      <div class="filter-right">
        <el-button :icon="Refresh" @click="refreshData"> 刷新 </el-button>
      </div>
    </div>

    <!-- 图表卡片 -->
    <el-card class="chart-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><TrendCharts /></el-icon>
            <span class="header-title">问题声量趋势</span>
          </div>
          <div class="header-right"></div>
        </div>
      </template>

      <div class="chart-content" v-loading="loading">
        <div
          ref="chartContainer"
          class="chart-container"
          :style="{ height: chartHeight + 'px' }"
        ></div>

        <div v-if="!loading && !hasData" class="empty-state">
          <el-empty description="暂无数据" />
        </div>

        <div v-if="!loading && hasData && isAllZeroData" class="no-volume-tip">
          <el-alert
            title="当前时间段内暂无声量数据"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>在所选时间范围内没有检测到声量数据，这可能是因为：</p>
              <ul>
                <li>该时间段内确实没有相关声量</li>
                <li>可以尝试调整时间范围查看其他时段的数据</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>
    <!-- 数据统计卡片 -->
    <el-row :gutter="16" class="stats-row" v-if="hasData">
      <!-- 总声量 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ totalVolume }}</div>
            <div class="stat-label">总声量</div>
          </div>
        </el-card>
      </el-col>

      <!-- 峰值声量 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ maxVolume }}</div>
            <div class="stat-label">峰值声量</div>
          </div>
        </el-card>
      </el-col>

      <!-- 峰值日期（独立卡片） -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ peakDate }}</div>
            <div class="stat-label">峰值日期</div>
          </div>
        </el-card>
      </el-col>

      <!-- 平均声量 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ avgVolume }}</div>
            <div class="stat-label">平均声量</div>
          </div>
        </el-card>
      </el-col>

      <!-- 趋势方向 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ trendDirection }}</div>
            <div class="stat-label">
              趋势方向
              <el-tooltip
                effect="dark"
                placement="top"
                content="如果后半段比前半段高出 10% 以上 → 上升 ↗，如果后半段比前半段低 10% 以上 → 下降 ↘，否则 → 平稳 →"
                raw-content
              >
                <el-icon class="question-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 源声详情表格 -->
    <el-card class="table-card" shadow="hover" v-if="hasData">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon class="header-icon"><Document /></el-icon>
            <span class="header-title">源声详情</span>
          </div>
          <div>
            <el-button
              @click="handleExportRes"
              v-if="hasExportPermission"
              :loading="exportLoading"
            >
              导出
            </el-button>
          </div>
        </div>
      </template>

      <div class="table-content" v-loading="tableLoading">
        <el-table
          :data="tableData"
          style="width: 100%"
          stripe
          :height="440"
          empty-text="暂无源声详情数据"
        >
          <el-table-column
            v-for="column in tableColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :min-width="column.minWidth"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row[column.prop] || "---" }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.pageNo"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from "vue";
import { useRoute } from "vue-router";
import {
  Refresh,
  TrendCharts,
  QuestionFilled,
  Document,
  Download,
} from "@element-plus/icons-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import weekOfYear from "dayjs/plugin/weekOfYear";

dayjs.extend(weekOfYear);
import { ElMessage } from "element-plus";
import { queryVolumeBySceneId } from "./api";
import { getOriginVolumeDetailById } from "@/views/sourceScene/sourceSceneManage/scenceApi";
import { isDev, WO_AUTH } from "@/utils/env";
import { csrfTokenManager } from "@/utils/CSRFTokenManager";
import { useUserStore } from "@/store";

// 接收父组件传递的props
const props = defineProps<{
  keyInfo: Array<{
    label: string;
    value: string;
    sceneId?: string;
    sceneName?: string;
  }>;
}>();

const route = useRoute();
const opinionIssueId = ref(route.query.id as string);
const userStore = useUserStore();
// 通用权限检查函数
const checkResourcePermission = (resourceId: string) => {
  if (!resourceId || !userStore.menuList) return false;
  // 检查菜单列表中是否包含指定资源ID
  return userStore.menuList.some((menu) => menu.resourceId === resourceId);
};
// 判断是否有导出权限
const hasExportPermission = computed(() => {
  return checkResourcePermission("ExportPublicOpinion");
});
// 从keyInfo中获取sceneId
const sceneId = computed(() => {
  const volumeInfo = props.keyInfo?.find((item) => item.label === "汇总声量");
  return volumeInfo?.sceneId || "";
});

const loading = ref(false);
const dateRange = ref<[string, string]>(["", ""]);
const timeUnit = ref("day");
const chartContainer = ref<HTMLElement>();
const chartInstance = ref<echarts.ECharts>();
const chartHeight = ref(400);

const chartData = ref<{
  dates: string[];
  volumes: number[];
}>({
  dates: [],
  volumes: [],
});

const hasData = computed(() => chartData.value.dates.length > 0);

const totalVolume = computed(() => {
  return chartData.value.volumes.reduce((sum, vol) => sum + vol, 0);
});

const maxVolume = computed(() => {
  return chartData.value.volumes.length > 0
    ? Math.max(...chartData.value.volumes)
    : 0;
});

const avgVolume = computed(() => {
  const total = totalVolume.value;
  const count = chartData.value.volumes.length;
  return count > 0 ? Math.round(total / count) : 0;
});

// 如果后半段比前半段高出 10% 以上 → “上升 ↗”
// 如果后半段比前半段低 10% 以上 → “下降 ↘”
const trendDirection = computed(() => {
  const volumes = chartData.value.volumes;
  if (volumes.length < 2) return "-";

  const firstHalf = volumes.slice(0, Math.floor(volumes.length / 2));
  const secondHalf = volumes.slice(Math.floor(volumes.length / 2));

  const firstAvg =
    firstHalf.reduce((sum, vol) => sum + vol, 0) / firstHalf.length;
  const secondAvg =
    secondHalf.reduce((sum, vol) => sum + vol, 0) / secondHalf.length;

  if (secondAvg > firstAvg * 1.1) return "上升 ↗";
  if (secondAvg < firstAvg * 0.9) return "下降 ↘";
  return "平稳 →";
});

const peakDate = computed(() => {
  const volumes = chartData.value.volumes;
  const dates = chartData.value.dates;
  if (volumes.length === 0) return "-";

  const maxIndex = volumes.indexOf(Math.max(...volumes));
  return dates[maxIndex] || "-";
});

const isAllZeroData = computed(() => {
  return (
    chartData.value.volumes.length > 0 &&
    chartData.value.volumes.every((v) => v === 0)
  );
});

// 表格相关数据
const tableLoading = ref(false);
const exportLoading = ref(false);
const tableData = ref<any[]>([]);
const pagination = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});

// 表格列定义
const tableColumns = ref([
  { prop: "appName", label: "应用名称", minWidth: "100" },
  { prop: "appPackageName", label: "应用包名", minWidth: "100" },
  { prop: "importDate", label: "导入日期", minWidth: "150" },
  { prop: "timePeriod", label: "时间分区", minWidth: "150" },
  { prop: "source", label: "问题来源", minWidth: "120" },
  { prop: "originOrder", label: "祥云单号", minWidth: "200" },
  { prop: "orderType", label: "工单类型", minWidth: "120" },
  {
    prop: "faultCategory",
    label: "问题分类",
    minWidth: "120",
  },
  { prop: "productType", label: "产品类型", minWidth: "120" },
  { prop: "productModel", label: "产品机型", minWidth: "130" },
  { prop: "osVersion", label: "系统版本", minWidth: "100" },
  { prop: "apiVersion", label: "API版本", minWidth: "120" },
  { prop: "appVersion", label: "应用版本", minWidth: "100" },
  { prop: "description", label: "源声描述", minWidth: "200" },
  { prop: "reportedTime", label: "问题发生时间", minWidth: "150" },
  { prop: "closeDate", label: "关单日期", minWidth: "150" },
  { prop: "originVolume", label: "源声声量", minWidth: "100" },
  { prop: "relatedSceneId", label: "场景分类编码", minWidth: "150" },
  { prop: "relatedSceneName", label: "场景名称", minWidth: "180" },
  { prop: "reportedPerson", label: "问题提出人", minWidth: "180" },
  { prop: "suggestResolveTime", label: "建议解决时间", minWidth: "180" },
  { prop: "remark", label: "备注", minWidth: "180" },
]);

const initDateRange = () => {
  const endDate = dayjs().format("YYYY-MM-DD");
  const startDate = dayjs().subtract(30, "day").format("YYYY-MM-DD");
  dateRange.value = [startDate, endDate];
};

const fetchVolumeData = async () => {
  if (!sceneId.value || !dateRange.value[0] || !dateRange.value[1]) {
    console.warn("缺少必要参数: sceneId 或 dateRange", {
      sceneId: sceneId.value,
      dateRange: dateRange.value,
    });
    return;
  }

  loading.value = true;
  try {
    const [startTime, endTime] = dateRange.value;
    const params = {
      sceneId: sceneId.value,
      startTime,
      endTime,
    };

    const response = await queryVolumeBySceneId(params);
    const processedData = processApiData(response);
    chartData.value = processedData;

    // 确保数据更新后再渲染图表
    await nextTick();
    // 额外延迟确保容器尺寸正确
    setTimeout(() => {
      renderChart();
    }, 50);
  } catch (error) {
    console.error("获取声量数据失败:", error);
    ElMessage.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理API返回的数据
const processApiData = (response: any) => {
  console.log("response:", response);
  const dates: string[] = [];
  const volumes: number[] = [];

  Object.keys(response).forEach((date) => {
    const volume = response[date];
    if (typeof volume === "number") {
      dates.push(formatDateByTimeUnit(date));
      volumes.push(volume);
    }
  });

  // 如果没有数据，生成占位数据以显示空图表
  if (dates.length === 0 && dateRange.value[0] && dateRange.value[1]) {
    const [startTime, endTime] = dateRange.value;
    const start = dayjs(startTime);
    const end = dayjs(endTime);
    const diffDays = end.diff(start, "day");

    // 根据时间单位生成空数据点
    let step = 1;
    if (timeUnit.value === "week") {
      step = 7;
    } else if (timeUnit.value === "month") {
      step = 30;
    }

    for (let i = 0; i <= Math.min(diffDays, 30); i += step) {
      const currentDate = start.add(i, "day");
      if (currentDate.isAfter(end)) break;

      dates.push(formatDateByTimeUnit(currentDate.format("YYYY-MM-DD")));
      volumes.push(0); // 使用0作为占位值
    }
  }

  // 按日期排序
  const combined = dates.map((date, index) => ({
    date,
    volume: volumes[index],
  }));
  combined.sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );
  return {
    dates,
    volumes,
  };
};

// 根据时间单位格式化日期
const formatDateByTimeUnit = (dateStr: string) => {
  const date = dayjs(dateStr);

  switch (timeUnit.value) {
    case "week":
      return date.format("YYYY-MM-DD") + " (第" + date.week() + "周)";
    case "month":
      return date.format("YYYY-MM");
    default:
      return date.format("YYYY-MM-DD");
  }
};

const renderChart = async () => {
  if (!chartContainer.value || !hasData.value) return;

  await nextTick();

  const containerRect = chartContainer.value.getBoundingClientRect();
  if (containerRect.width === 0 || containerRect.height === 0) {
    setTimeout(() => renderChart(), 100);
    return;
  }

  if (chartInstance.value) {
    chartInstance.value.dispose();
    chartInstance.value = undefined;
  }

  chartInstance.value = echarts.init(chartContainer.value, null, {
    width: chartContainer.value.clientWidth || 800,
    height: chartHeight.value,
    renderer: "canvas",
  });

  const option = {
    title: {
      show: false,
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e4e7ed",
      borderWidth: 1,
      textStyle: {
        color: "#606266",
        fontSize: 12,
      },
      axisPointer: {
        type: "line",
        lineStyle: {
          color: "#409eff",
          width: 1,
          type: "dashed",
        },
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "8%",
      top: "5%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.value.dates,
      axisLine: {
        lineStyle: {
          color: "#e4e7ed",
        },
      },
      axisLabel: {
        color: "#909399",
        fontSize: 11,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1, // 确保y轴刻度为整数
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: "#909399",
        fontSize: 11,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: "#f5f7fa",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: "声量",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: (value: number) => (value > 0 ? 6 : 4),
        lineStyle: {
          width: 3,
          color: chartData.value.volumes.every((v) => v === 0)
            ? "#d3d3d3"
            : {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: "#409eff" },
                  { offset: 1, color: "#67c23a" },
                ],
              },
        },
        itemStyle: {
          color: (params: any) => (params.value > 0 ? "#409eff" : "#d3d3d3"),
          borderColor: "#fff",
          borderWidth: 2,
        },
        areaStyle: chartData.value.volumes.every((v) => v === 0)
          ? {
              color: "rgba(211, 211, 211, 0.1)",
            }
          : {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: "rgba(64, 158, 255, 0.3)" },
                  { offset: 1, color: "rgba(64, 158, 255, 0.05)" },
                ],
              },
            },
        data: chartData.value.volumes,
        label: {
          show: true,
          position: "top",
          formatter: "{c}",
          fontSize: 11,
          color: "#606266",
          fontWeight: 500,
          offset: [0, -8],
          backgroundColor: "rgba(255, 255, 255, 0.8)",
          borderRadius: 3,
          padding: [2, 4],
          shadowBlur: 2,
          shadowColor: "rgba(0, 0, 0, 0.1)",
          shadowOffsetX: 0,
          shadowOffsetY: 1,
        },
        emphasis: {
          focus: "series",
          itemStyle: {
            color: "#409eff",
            borderColor: "#fff",
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: "rgba(64, 158, 255, 0.3)",
          },
          label: {
            show: true,
            fontSize: 12,
            fontWeight: 600,
            color: "#409eff",
            offset: [0, -10],
          },
        },
        markText: chartData.value.volumes.every((v) => v === 0)
          ? {
              data: [
                {
                  coord: [Math.floor(chartData.value.dates.length / 2), 0],
                  value: "暂无数据",
                  itemStyle: {
                    color: "#999",
                  },
                },
              ],
            }
          : undefined,
      },
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: "cubicOut",
  };

  chartInstance.value.setOption(option as any);

  window.addEventListener("resize", handleResize);
};

const handleDateChange = () => {
  fetchVolumeData();
  fetchTableData();
};

const handleTimeUnitChange = () => {
  fetchVolumeData();
};

const refreshData = () => {
  fetchVolumeData();
  fetchTableData();
};

// 获取表格数据
const fetchTableData = async () => {
  if (!sceneId.value || !dateRange.value[0] || !dateRange.value[1]) {
    return;
  }

  tableLoading.value = true;
  try {
    const params = {
      pageNo: pagination.value.pageNo,
      pageSize: pagination.value.pageSize,
      sceneId: sceneId.value,
      startTime: dateRange.value[0],
      endTime: dateRange.value[1],
    };

    console.log("获取源声详情参数:", params);
    const response = await getOriginVolumeDetailById(params);
    console.log("response11:", response);

    // 处理API响应数据结构
    const result = response;
    tableData.value = result.data || [];
    pagination.value.total = result.total || 0;
    pagination.value.pageNo = result.pageNo || 1;
    pagination.value.pageSize = result.pageSize || 20;
  } catch (error) {
    console.error("获取源声详情失败:", error);
    ElMessage.error("获取源声详情失败，请稍后重试");
    tableData.value = [];
  } finally {
    tableLoading.value = false;
  }
};

// 分页处理
const handleSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize;
  pagination.value.pageNo = 1;
  fetchTableData();
};

const handleCurrentChange = (pageNo: number) => {
  pagination.value.pageNo = pageNo;
  fetchTableData();
};

// 导出功能
const handleExportRes = () => {
  return new Promise((resolve, reject) => {
    const req = new XMLHttpRequest();
    exportLoading.value = true;
    const queryParams = {
      pageNo: 1,
      pageSize: 99999,
      sceneId: sceneId.value,
    };
    req.open(
      "POST",
      `${window.location.origin}${
        import.meta.env.VITE_APP_BASE_API
      }/wiseoperOriginData/exportData`,
      true
    );
    req.responseType = "blob";
    req.setRequestHeader("Content-Type", "application/json");
    // 使用新的 CSRFTokenManager 获取 CSRF 令牌
    const csrfToken = csrfTokenManager.getToken();
    if (csrfToken) {
      req.setRequestHeader("X-CSRF-TOKEN", csrfToken);
    }
    if (isDev()) {
      req.setRequestHeader("token", atob(WO_AUTH));
    }
    req.onload = function () {
      const data = req.response;
      if (req.status === 413) {
        exportLoading.value = false;
        reject(new Error("导出数据量过大，限制50000条。请修改查询条件后重试"));
        return;
      }

      if (data.size === 0) {
        exportLoading.value = false;
        reject(new Error("没有数据可导出"));
        return;
      }
      const blob = new Blob([data]);
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.download = "问题源声详情导出.xlsx";
      a.href = blobUrl;
      a.click();
      exportLoading.value = false;
      resolve(true);
    };
    req.send(JSON.stringify(queryParams));
  });
};

const handleResize = () => {
  if (chartInstance.value) {
    // 延迟调整大小，确保容器尺寸已更新
    setTimeout(() => {
      if (chartInstance.value) {
        chartInstance.value.resize();
      }
    }, 100);
  }
};

// ResizeObserver实例
let resizeObserver: ResizeObserver | null = null;

onMounted(() => {
  initDateRange();
  fetchVolumeData();

  // 监听容器尺寸变化
  nextTick(() => {
    if (chartContainer.value && window.ResizeObserver) {
      resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === chartContainer.value) {
            handleResize();
          }
        }
      });
      resizeObserver.observe(chartContainer.value);
    }
  });

  // 备用的window resize监听
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  window.removeEventListener("resize", handleResize);
});

watch(
  () => [dateRange.value, timeUnit.value],
  () => {
    if (dateRange.value[0] && dateRange.value[1]) {
      fetchVolumeData();
      fetchTableData();
    }
  },
  { deep: true }
);

// 监听props变化
watch(
  () => props.keyInfo,
  () => {
    if (sceneId.value && dateRange.value[0] && dateRange.value[1]) {
      fetchVolumeData();
      fetchTableData();
    }
  },
  { deep: true, immediate: true }
);
</script>

<style lang="less" scoped>
.issue-volume-container {
  background: #f5f7fa;
  min-height: calc(100vh - 200px);
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .filter-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .time-unit-group {
    :deep(.el-radio-button__inner) {
      border-radius: 6px;
      margin: 0 2px;
      border: 1px solid #dcdfe6;
      background: #fff;
      color: #606266;
      transition: all 0.3s;

      &:hover {
        color: #409eff;
        border-color: #409eff;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      background: #409eff;
      border-color: #409eff;
      color: #fff;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
    }
  }
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  color: #0c0f14;

  :deep(.el-card__header) {
    padding: 20px 24px;
    // background: linear-gradient(135deg, #7a8de2 0%, #764ba2 100%);
    border-bottom: none;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .header-icon {
        font-size: 20px;
        color: #409eff;
      }

      .header-title {
        font-size: 18px;
        font-weight: 600;
      }
    }

    .header-right {
      :deep(.el-tag) {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        font-weight: 500;
      }
    }
  }

  :deep(.el-card__body) {
    padding: 24px;
  }
}

.chart-content {
  position: relative;
  min-height: 400px;

  .chart-container {
    width: 100%;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .no-volume-tip {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    z-index: 10;

    :deep(.el-alert) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-alert__content) {
      ul {
        margin: 8px 0 0 0;
        padding-left: 20px;

        li {
          margin: 4px 0;
          color: #666;
        }
      }
    }
  }
}

.stats-row {
  margin-top: 20px;

  :deep(.el-col) {
    flex: 0 0 20%;
    max-width: 20%;
    box-sizing: border-box;
  }

  .stat-card {
    border-radius: 8px;
    transition: all 0.3s;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }

    :deep(.el-card__body) {
      padding: 20px;
    }

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 28px;
        font-weight: 700;
        color: #0c0f14; // 改为黑色系
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #0c0f14; // 改为黑色系
        font-weight: 500;
      }

      .stat-sub {
        margin-top: 2px;
        font-size: 12px;
        color: #0c0f14; // 改为黑色系
        opacity: 0.8;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .issue-volume-container {
    padding: 12px;
  }

  .filter-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;

    .filter-left {
      flex-direction: column;
      gap: 12px;
    }
  }

  .stats-row {
    :deep(.el-col) {
      margin-bottom: 16px;
    }
  }

  .chart-content {
    min-height: 300px;

    .chart-container {
      height: 300px !important;
    }
  }
}

// 加载动画
:deep(.el-loading-mask) {
  border-radius: 8px;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// 表格卡片样式
.table-card {
  margin-top: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  color: #0c0f14;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .header-icon {
        font-size: 20px;
        color: #409eff;
      }

      .header-title {
        font-size: 18px;
        font-weight: 600;
      }
    }

    .header-right {
      :deep(.el-button) {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #fff;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }

  :deep(.el-card__body) {
    padding: 24px;
  }
}

.table-content {
  .pagination-wrapper {
    display: flex;
    justify-content: end;
    padding-top: 20px;
  }

  :deep(.el-table) {
    .el-table__header {
      th {
        background-color: #f8f9fa;
        color: #606266;
        font-weight: 600;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
