#!/bin/bash
# ***********************************************************************
# Copyright: (c) Huawei Technologies Co., Ltd. 2021. All rights reserved.
# script for build
# version: 1.0.0
# change log:
# ***********************************************************************
set -eux
set -o pipefail

BASE_PATH=$(cd $(dirname $0);pwd)
PROJECT_PATH=$(cd $BASE_PATH/..;pwd)

SERVICE_NAME=$1
PACKAGE_VERSION=$2

function copy_all_package() {
  # Copy the service package to the ${WORKSPACE} path.
  cp "${PROJECT_PATH}"/dist.zip "${WORKSPACE}"

  # Copy the IAC package to the ${WORKSPACE} path.
  # find "${IAC_PATH}"/ -name "*.zip" | xargs cp -t "${WORKSPACE}"
  # find "${WISEDB_PATH}"/ -name "*.zip" | xargs cp -t "${WORKSPACE}"
}

function iacPackage() {
    cd ${PROJECT_PATH}
    cd iac
    cat global/PartnerVODSolutionService/resources.yaml |tr '#VERSION' ${PACKAGE_VERSION} > global/PartnerVODSolutionService/resources.yaml
    cat package.json |tr '#VERSION' ${PACKAGE_VERSION} > package.json
    zip -r iac.zip *
}

#function modify_assemble_package_metadata() {
#  # Modifying the Metadata of the Package.json Package
#  cd "${WORKSPACE}"
#
#  #add package.json to Assemble package
#  echo "{
#  \"type\": \"combination\",
#  \"service\": \"${SERVICE_NAME}\",
#  \"version\": \"${PACKAGE_VERSION}\",
#  \"sequence\": \"[]\",
#  \"packages\": {
#  	\"dist.zip\"
#  	}
#  } " | python -m json.tool >"${WORKSPACE}"/package.json
#}
#
#function build_combine_package() {
#  cd "${WORKSPACE}"
#  PackageName="${SERVICE_NAME}_Iac_${PACKAGE_VERSION}(MCOM).zip"
#  mv dist.zip ${PackageName}
#}
#
#function upload_combine_package() {
#  bash "${WORKSPACE}"/Script/clouddragon/build2.0/service/getPackageInfo.sh "" "${PackageName}"
#}


function main() {
  copy_all_package
#  modify_assemble_package_metadata
  build_combine_package
  upload_combine_package
}

main
