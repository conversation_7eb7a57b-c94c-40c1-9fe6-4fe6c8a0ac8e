#!/bin/bash
JAVA_OPTS="$JAVA_OPTS -Dlog4j2.formatMsgNoLookups=true"
BIN_PATH=`dirname $0`
APP_ROOT=$BIN_PATH/..

bash $BIN_PATH/stop.sh "$@"

export RASP_SWITCH=yes
export JAVA_OPTS="$JAVA_OPTS -Djdk.tls.rejectClientInitiatedRenegotiation=true"
export TRACER_AGENT_SWITCH="ON"
# 以下修改成业务的微服务！
export TRACER_MICROSERVICE_NAME="PartnerVOCMngPortal"

bash $APP_ROOT/nuwa/bin/startup.sh
#no sc, add cse-sc-locals
#bash $APP_ROOT/nuwa/bin/startup.sh --cse-sc-local
if [ $# -ne 1 -o "$1" != "watchdog" ];then
    bash $APP_ROOT/nuwa/watchdog/bin/watchdog.sh start > /dev/null 2>&1
    bash $APP_ROOT/nuwa/watchdog/bin/watchdog.sh mon > /dev/null 2>&1
fi


