<template>
  <div class="card relate-order-card" style="margin-top: 20px">
    <div class="title">审核定界结论</div>
    <div class="description">进展信息</div>
    <el-form
      :model="progressData"
      class="form"
      label-width="auto"
      :disabled="!canEdit"
      :rules="progressRules"
      ref="progressRef"
    >
      <el-form-item
        label="审核结论"
        :label-position="'top'"
        class="form-item"
        prop="auditConclusion"
      >
        <el-input
          type="textarea"
          placeholder="请输入内容"
          v-model="progressData.auditConclusion"
          show-word-limit
          maxlength="2000"
          @blur="
            progressData.auditConclusion = progressData.auditConclusion.trim()
          "
        />
      </el-form-item>
    </el-form>
    <div class="description" v-if="!isNodeFinished && canEdit">操作信息</div>
    <el-form
      ref="formRef"
      label-position="top"
      :model="formData"
      v-if="!isNodeFinished && canEdit"
      :rules="rulesAssignment"
      label-width="95"
    >
      <div style="margin-top: 24px; display: flex; align-items: center">
        <el-form-item
            label="操作类型"
            class="form-item"
            style="margin-right: 20px"
            prop="operationType"
        >
          <el-radio-group
              @change="changeOperationType"
              v-model="formData.operationType"
          >
            <el-radio :value="operationTypes[1].value" v-if="!isNonQuestion">{{
                operationTypes[1].label
              }}</el-radio>
            <el-radio :value="operationTypes[0].value">{{
                operationTypes[0].label
              }}</el-radio>
            <el-radio :value="operationTypes[2].value">{{
                operationTypes[2].label
              }}</el-radio>
            <el-radio :value="operationTypes[4].value" v-if="isNonQuestion">{{
                operationTypes[4].label
              }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
            label="指定处理人"
            v-if="
          formData.operationType !== '0002' && formData.operationType !== '1000'
        "
            class="form-item"
            prop="nextHandler"
            style="margin-left: 24px; width: 20%"
        >
          <el-select
              v-model="formData.nextHandler"
              placeholder="请选择人员"
              filterable
          >
            <el-option
                v-for="item in handlerOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <el-form-item
          :label="formData.operationType==='0000'?'转单原因':'驳回原因'"
          :label-position="'top'"
          v-if="formData.operationType==='0000'||formData.operationType==='0002'"
          class="form-item"
          prop="reason"
      >
        <el-input
            type="textarea"
            :placeholder="formData.operationType==='0000'?'请输入转单原因':'请输入驳回原因'"
            v-model="formData.reason"
            show-word-limit
            maxlength="2000"
            @blur="
            formData.reason = formData.reason.trim()
          "
        />
      </el-form-item>
    </el-form>
    <div v-if="canEdit" class="operate-button-container text-center">
      <el-button id="save-button" @click="onSave">{{
        isNodeFinished ? "更新信息" : "保存"
      }}</el-button>
      <el-button
        v-if="!isNodeFinished"
        id="assign-button"
        type="primary"
        @click="onSubmit"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, toRefs } from "vue";
import { useRoute } from "vue-router";
import { ElLoading, ElMessage, type FormInstance } from "element-plus";
import {
  fetchDeLmitProblemData,
  operationTypeOptions as operationTypes,
} from "@/business/progressInformation/delimitProblem";
import { rulesAssignment } from "@/business/progressInformation/replicateProblem";
import { getHandlers } from "@/business/progressInformation/common/getHandlers";
import { ewpService } from "@/utils/axios";
import { useFlowInfoStore } from "@/store";
import { cloneDeep, isEqual } from "lodash-es";
import { getValidateFun } from "@/business/progressInformation/common/validateUtil";

const emit = defineEmits(["refresh"]);

const refreshParentData = (step = null) => {
  emit("refresh", step);
};
const props = defineProps({
  canEdit: {
    default: () => false,
    required: true,
  },
  node: {
    default: () => {},
    required: true,
  },
});

const flowInfoStore = useFlowInfoStore();

const progressRef = ref<FormInstance>();

const formRef = ref<FormInstance>();

const progressRules = {
  auditConclusion: [
    { required: true, message: "结论必填！" },
    { validator: getValidateFun("结论"), trigger: "blur" },
  ],
};

const isNonQuestion = ref(false);

const { node } = toRefs(props);
const isNodeFinished = node.value.isFinished;

const route = useRoute();
const opinionIssueId = route.query.id;

const handlerOptions = ref([]);

const progressData = reactive({
  auditConclusion: "",
});

const formData = reactive({
  operationType: "",
  nextHandler: "",
  reason: ""
});

const changeOperationType = async (operationType) => {
  formData.reason = "";
  if (operationType === "1000" || operationType === "0002") {
    return;
  }
  const loading = ElLoading.service({
    lock: true,
    background: "rgba(0, 0, 0, 0.7)",
    text: "Loading",
  });
  let handlerList = null;
  if (opinionIssueId) {
    handlerList = await getHandlers(opinionIssueId, operationType);
  }
  if (handlerList) {
    handlerOptions.value = handlerList.handlers;
    formData.nextHandler = handlerList.defaultHandler;
  } else {
    ElMessage({
      type: "error",
      message: "获取下一步处理人失败",
    });
  }
  loading.close();
};

const onSubmit = async () => {
  if (!formRef.value) return;
  if (!progressRef.value) return;
  if (formData.operationType === "0002") {
    try {
      if (!formData.reason){
        ElMessage({
          type: "error",
          message: "请填写原因",
        });
        return false;
      }
      const requestData = {
        opinionIssueId: opinionIssueId,
        assignedPerson: flowInfoStore.getLastPerson(2),
        operateType: formData.operationType,
        auditConclusion: progressData.auditConclusion,
        reason: formData.reason
      };

      await ewpService.post("/standardReplyThreeNew/submit", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      refreshParentData();
      ElMessage({
        type: "success",
        message: "驳回成功",
      });
    } catch (err) {
      ElMessage({
        type: "success",
        message: "驳回失败",
      });
    }
  } else if (formData.operationType === "0000") {
    formRef.value.validate(async (valid2, fields2) => {
      try {
        if (valid2) {
          const requestData = {
            opinionIssueId: opinionIssueId,
            assignedPerson: formData.nextHandler,
            operateType: formData.operationType,
            auditConclusion: progressData.auditConclusion,
            reason: formData.reason
          };
          await ewpService.post("/standardReplyThreeNew/submit", requestData, {
            headers: {
              "Content-Type": "application/json",
            },
          });
          ElMessage({
            type: "success",
            message: "转单成功",
          });
          refreshParentData();
        }
      } catch (err) {
        ElMessage({
          type: "error",
          message: "转单失败",
        });
      }
    });
  } else {
    progressRef.value.validate(async (valid, fields) => {
      formRef.value.validate(async (valid2, fields2) => {
        try {
          if (valid && valid2) {
            const requestData = {
              opinionIssueId: opinionIssueId,
              assignedPerson: formData.nextHandler,
              operateType:
                formData.operationType === "1000"
                  ? "0001"
                  : formData.operationType,
              auditConclusion: progressData.auditConclusion,
            };

            await ewpService.post(
              "/standardReplyThreeNew/submit",
              requestData,
              {
                headers: {
                  "Content-Type": "application/json",
                },
              }
            );
            refreshParentData();
            ElMessage({
              type: "success",
              message: "提交成功",
            });
          }
        } catch (err) {
          ElMessage({
            type: "error",
            message: "提交失败",
          });
        }
      });
    });
  }
};

const onSave = async () => {
  if (!progressData.auditConclusion) {
    ElMessage({
      type: "error",
      message: "审核结论必填！",
    });
    return;
  }
  if (node.value.isFinished) {
    try {
      if (isEqual(originData.value, progressData)) {
        ElMessage({
          type: "error",
          message: "内容未修改,请修改内容后保存",
        });
        return false;
      }

      const requestData = {
        opinionIssueId: opinionIssueId,
        assignedPerson: formData.nextHandler,
        operateType: "0001",
        auditConclusion: progressData.auditConclusion,
      };

      await ewpService.post("/standardReplyThreeNew/submit", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      ElMessage({
        type: "success",
        message: "更改成功",
      });
      refreshParentData(node.value.step);
    } catch (err) {
      ElMessage({
        type: "error",
        message: "更改失败",
      });
    }
  } else {
    try {
      const requestData = {
        opinionIssueId: opinionIssueId,
        assignedPerson: formData.nextHandler,
        operationType: formData.operationType,
        auditConclusion: progressData.auditConclusion,
      };

      await ewpService.post("/standardReplyThreeNew/save", requestData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      ElMessage({
        type: "success",
        message: "提交成功",
      });
      refreshParentData(node.value.step);
    } catch (err) {
      ElMessage({
        type: "error",
        message: "提交失败",
      });
    }
  }
};

const originData = ref({});

// 获取已有数据
const fetchExistingData = async () => {
  if (!opinionIssueId) return;

  try {
    const response = await ewpService.get(
      `/standardReplyThreeNew/queryMaxVersionData?opinionIssueId=${opinionIssueId}`
    );
    if (response) {
      progressData.auditConclusion = response.auditConclusion;
      isNonQuestion.value = response.issueAttribution;
    }
    originData.value = cloneDeep(progressData);
  } catch (error) {
    ElMessage({
      type: "error",
      message: "查询失败",
    });
  }
};
onMounted(async () => {
  fetchExistingData();
});
</script>

<style lang="scss" scoped>
@import "@/styles/common-styles.scss";
.card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
}
:deep(.el-form--inline.el-form--label-top){
  display: block;
}
</style>
