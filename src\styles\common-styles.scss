.card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
}
.required-field {
  color: #f56c6c;
  margin-left: 4px;
}
.title {
  color: rgb(25, 25, 25);
  font-family: 鸿蒙黑体;
  font-size: 20px;
  font-weight: 700;
  line-height: 150%;
  letter-spacing: 0%;
  text-align: left;
}

#add {
  display: block;
  margin-top: 16px;
}

.table {
  margin-top: 16px;
}

.operate-button-container {
  margin-top: 16px;
  #save-button,
  #assign-button {
    width: 96px;
  }
}

.description {
  color: rgba(25, 25, 25, 0.6);
  font-family: "鸿蒙黑体";
  background: rgb(245, 245, 245);
  width: 100%;
  height: 40px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: start;
  font-size: 20px;
  color: rgb(25, 25, 25);
  font-size: 14px;
  font-weight: 700;
  padding-left: 12px;
}

.relate-order-card {
  .description {
    color: rgba(25, 25, 25, 0.6);
    font-family: "鸿蒙黑体";
    background: rgb(245, 245, 245);
    width: 100%;
    height: 40px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: start;
    font-size: 20px;
    color: rgb(25, 25, 25);
    font-size: 14px;
    font-weight: 700;
    padding-left: 12px;
  }

  .form {
    margin-top: 16px;
  }
}

.mt-20 {
  margin-top: 20px;
  display: flex;
  align-items: center;
}

.text-center {
  text-align: center;
}
