<template>
  <el-button @click="showSetColumnDialog">列表设置</el-button>
  <el-dialog
    v-model="showDialog"
    title="列表设置"
    width="62.5%"
    style="min-width: 900px"
  >
    <el-row :gutter="0" class="column-box">
      <el-col :span="16" class="left" style="height: 100%">
        <div class="title">列项自定义</div>
        <div class="bottom left">
          <el-checkbox @change="checkedAll" v-model="selectAll">
            全选
          </el-checkbox>
          <el-checkbox-group v-model="checkedArray">
            <el-row>
              <el-col v-for="column in allColumn" :key="column.prop" :span="6">
                <el-checkbox
                  :title="column.label"
                  :label="column.label"
                  :value="column.prop"
                  @change="
                    (value) => {
                      checkedItemChange(value, column);
                    }
                  "
                  class="checkbox-ellipsis"
                >
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </div>
      </el-col>
      <el-col :span="8" style="height: 100%">
        <div class="title">
          已选择列<span style="color: var(--primary-color)"
            >(可拖动列进行排序)</span
          >
        </div>
        <el-scrollbar class="bottom right">
          <VueDraggableNext
            class="dragArea"
            :list="selectColumn"
            @change="dragChange"
          >
            <div
              class="list-item"
              v-for="(column, index) in selectColumn"
              :key="column.prop"
            >
              <el-icon class="drag-icon"><Operation /></el-icon>
              <el-text class="drag-text" line-clamp="1">
                {{ column.label }}
              </el-text>
              <el-icon class="drag-icon" @click="delColumn(index)"
                ><Close
              /></el-icon>
            </div>
          </VueDraggableNext>
        </el-scrollbar>
      </el-col>
    </el-row>
    <div style="text-align: end">
      <el-button @click="resetColumn">重置</el-button>
      <el-button @click="showDialog = false">取消</el-button>
      <el-button type="primary" @click="saveColumn">应用</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { VueDraggableNext } from "vue-draggable-next";
import { Close } from "@element-plus/icons-vue";
import { ref, watch, defineProps, defineEmits } from "vue";
import { cloneDeep } from "lodash-es";
import { storage } from "@/utils/Storage";
import { Operation } from "@element-plus/icons-vue";
import { ElMessage } from 'element-plus';
const showDialog = ref(false);
const { allColumn, defaultColumn, localStorageName } = defineProps({
  // 所有列
  allColumn: {
    default: () => [],
    required: true,
  },
  // 默认列
  defaultColumn: {
    default: () => [],
    required: true,
  },
  localStorageName: {
    default: () => "",
    required: true,
  },
});
const emit = defineEmits(["saveHandle"]);
// 选中列
const selectColumn = ref([]);
const checkedArray = ref(selectColumn.value.map((v) => v.prop));
const selectAll = ref(false);
// 删除列
const delColumn = (index) => {
  selectColumn.value.splice(index, 1);
};
watch(
  () => selectColumn.value,
  () => {
    checkedArray.value = selectColumn.value.map((v) => v.prop);
    selectAll.value = allColumn.length === selectColumn.value.length;
  },
  { deep: true }
);
const dragChange = (event) => {};
/**
 * 全选
 */
const checkedAll = (value) => {
  if (value) {
    cloneDeep(allColumn).forEach((v) => {
      const index = selectColumn.value.findIndex((c) => c.prop === v.prop);
      if (index === -1) {
        selectColumn.value.push(v);
      }
    });
  } else {
    selectColumn.value = [];
  }
};
/**
 * item选中
 * value true选中 false取消选中
 */
const checkedItemChange = (value, columnItem) => {
  if (value) {
    selectColumn.value.push(columnItem);
  } else {
    const index = selectColumn.value.findIndex(
      (v) => v.prop === columnItem.prop
    );
    selectColumn.value.splice(index, 1);
  }
};
const showSetColumnDialog = () => {
  const localStorageClounm = storage.get(localStorageName);
  if (localStorageClounm) {
    selectColumn.value = cloneDeep(localStorageClounm);
  } else {
    selectColumn.value = cloneDeep(defaultColumn);
  }
  checkedArray.value = selectColumn.value.map((v) => v.prop);
  selectAll.value = allColumn.length === selectColumn.value.length;
  showDialog.value = true;
};
// 重置
const resetColumn = () => {
  selectColumn.value = cloneDeep(defaultColumn);
};
// 保存
const saveColumn = () => {
  if(!selectColumn.value.length) {
    ElMessage({
      type: 'error',
      message: '至少选择一列'
    });
    return;
  }
  storage.set(localStorageName, cloneDeep(selectColumn.value));
  showDialog.value = false;
  emit("saveHandle", selectColumn.value);
};
</script>
<style lang="scss" scoped>
.column-box {
  border: 1px solid #c9c9c9;
  border-radius: 8px;
  margin-bottom: 24px;
  overflow: hidden;
  color: var(--default-color);
  height: 636px;
  > .left {
    border-right: 1px solid #c9c9c9;
  }
  .title {
    width: 100%;
    text-align: start;
    border-bottom: 1px solid #c9c9c9;
    height: 48px;
    line-height: 48px;
    font-size: 16px;
    padding-left: 24px;
  }
  .bottom {
    width: 100%;
    height: calc(100% - 49px);
    overflow: hidden;
    text-align: start;
    box-sizing: border-box;
    &.left {
      padding: 12px 24px;
      .el-checkbox {
        width: 100%;
        overflow: hidden;
      }
    }
    &.right {
      padding: 0 12px;
    }
    .dragArea {
      &:first-child {
        margin-top: 12px;
      }
      &:last-child {
        margin-bottom: 16px;
      }
      .list-item {
        padding: 8px 0;
        font-size: 14px;
        border-bottom: 1px solid #ececee;
        display: flex;
        align-items: center;
        .drag-icon {
          width: 16px;
          margin: 0 8px;
        }
        .drag-text {
          flex-grow: 1;
          overflow: hidden;
        }
        &:last-child {
          border-bottom: unset;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.checkbox-ellipsis.el-checkbox {
  .el-checkbox__label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>