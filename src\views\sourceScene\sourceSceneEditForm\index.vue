<template>
  <div>
    <el-form
      ref="formRef"
      :model="formData"
      label-width="109"
      label-position="left"
    >
      <el-row :gutter="60">
        <el-col
          v-for="item in EditComponentsData"
          :key="item.key"
          :span="isHideKeys.includes(item.key) ? 0 : 12"
        >
          <el-form-item
            :label="item.label"
            :prop="item.key"
            :rules="item.rules"
            :class="item.rules?.required ? '' : 'unRequired'"
            v-if="!isHideKeys.includes(item.key)"
          >
            <el-input
              v-if="item.component === 'Input'"
              placeholder="请输入"
              v-model="formData[item.key]"
              :disabled="disabledKeys.includes(item.key)"
            />
            <el-date-picker
              v-if="item.component === 'DatePicker'"
              placeholder="请选择"
              :disabled-date="item.disabledDate"
              v-model="formData[item.key]"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="disabledKeys.includes(item.key)"
            />
            <el-select
              v-if="item.component === 'Select'"
              placeholder="请选择"
              :multiple="item.multiple"
              collapse-tags
              :max-collapse-tags="1"
              v-model="formData[item.key]"
              @change="selectChange(item.key)"
              filterable
              :disabled="disabledKeys.includes(item.key)"
            >
              <template
                #header
                v-if="item.key === 'productModel' || item.key === 'productType'"
              >
                <el-checkbox
                  label="全选"
                  v-model="productCheckAll[item.key]"
                  :indeterminate="productIndeterminate[item.key]"
                  @change="(value) => checkAllChangeHandle(value, item.key)"
                />
              </template>
              <div v-if="item.key === 'productModel'">
                <el-option
                  v-for="o in productModelList"
                  :label="o.label"
                  :value="o.label"
                  :key="o.value"
                />
              </div>
              <div v-else>
                <el-option
                  v-for="o in item.option"
                  :label="o.label"
                  :value="o.value"
                  :key="o.value"
                />
              </div>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="text-align: end">
      <el-button @click="cancelClick">取消</el-button>
      <el-button type="primary" @click="submitClick">确定</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { productMap } from "@/components/issuesOpinionManage/commonArea/checkList";
import { cloneDeep } from "lodash-es";
import { ref, reactive, defineProps, watch, defineEmits, nextTick } from "vue";
import {
  EditComponentsData,
  productTypeList,
  allProductModelList,
  SELECT_ALL,
  MultipleKeys,
} from "./index";
const { disabledKeys, rowData, isHideKeys } = defineProps({
  // 不可编辑字段
  disabledKeys: {
    default: () => [],
    required: true,
  },
  // 行内数据
  rowData: {
    default: () => {
      return {};
    },
    required: true,
  },
  // 隐藏的字段
  isHideKeys: { default: () => [] },
});
const emit = defineEmits(["cancelHandle", "submitHandle"]);
const formRef = ref();
const formData = reactive(rowData);
/**
 * 取消
 */
const cancelClick = () => {
  emit("cancelHandle", formData);
};
/**
 * 确定
 */
const submitClick = () => {
  formRef.value.validate(async (valid, fields) => {
    if (valid) {
      // 手动去除字符串前后空格
      Object.keys(formData).forEach((key) => {
        if (typeof formData[key] === "string") {
          formData[key] = formData[key].trim();
        }
      });
      const submitData = cloneDeep(formData);
      MultipleKeys.forEach((key) => {
        submitData[key] = submitData[key] ? submitData[key].join(",") : "";
      });
      emit("submitHandle", submitData);
    }
  });
};
// --------------- 编辑弹窗中，产品类型、产品机型逻辑 -----------
// 产品机型下拉框
const productModelList = ref([]);
// 下拉筛选，自定义头部全选
const productCheckAll = reactive({ productType: false, productModel: false });
const productIndeterminate = reactive({
  productType: false,
  productModel: false,
});
const checkAllChangeHandle = (val, key) => {
  productIndeterminate[key] = false;
  if (val) {
    if (key === "productType") {
      formData.productType = [SELECT_ALL];
      formData.productModel = [];
    }
    if (key === "productModel") {
      formData.productModel = [SELECT_ALL];
    }
  } else {
    if (key === "productType") {
      formData.productType = [];
      formData.productModel = [];
    }
    if (key === "productModel") {
      formData.productModel = [];
    }
  }
};
// 监听产品类型变化，改变选择的产品机型列表
watch(
  () => formData.productType,
  (productTypeData) => {
    productModelList.value = [];
    if (!productTypeData.length) {
      productModelList.value = [];
    } else if (productTypeData.includes(SELECT_ALL)) {
      productModelList.value = cloneDeep(allProductModelList);
    } else {
      productTypeData.forEach((v) => {
        if (productMap[v]) {
          productModelList.value.push(...productMap[v]);
        }
      });
    }
    nextTick(() => {
      setSelectStyle();
    });
  },
  { immediate: true }
);
watch(
  () => formData.productModel,
  (productModelData) => {
    setSelectStyle();
  }
);
const selectChange = (keyValue) => {
  if (keyValue === "productType") {
    formData.productType = formData.productType.filter((v) => v !== SELECT_ALL);
    formData.productModel = [];
  }
  if (keyValue === "productModel") {
    formData.productModel = formData.productModel.filter(
      (v) => v !== SELECT_ALL
    );
  }
};
/**
 * 设置全选样式
 */
const setSelectStyle = () => {
  if (formData.productType.length === 0) {
    productCheckAll.productType = false;
    productIndeterminate.productType = false;
  } else if (
    formData.productType.length === productTypeList.length ||
    formData.productType.includes(SELECT_ALL)
  ) {
    productCheckAll.productType = true;
    productIndeterminate.productType = false;
  } else {
    productIndeterminate.productType = true;
  }
  if (formData.productModel.length === 0) {
    productCheckAll.productModel = false;
    productIndeterminate.productModel = false;
  } else if (
    formData.productModel.length === productModelList.value.length ||
    formData.productModel.includes(SELECT_ALL)
  ) {
    productCheckAll.productModel = true;
    productIndeterminate.productModel = false;
  } else {
    productIndeterminate.productModel = true;
  }
};
</script>
