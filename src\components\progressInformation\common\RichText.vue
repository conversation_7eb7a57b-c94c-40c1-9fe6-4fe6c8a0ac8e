<template>
  <div :id="`wang-editor-${props.field}`"></div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { ElMessage } from 'element-plus'
import WangEditor from 'wangeditor';
import { getAuthCode, upload } from '@/business/progressInformation/common/upload'
import xss from 'xss'

const props = defineProps({
  modelValue: {
    required: true,
    default: () => '',
  },
  contentMaxLength: {
    required: false,
    default: () => 2000,
  },
  field: {
    required: true,
    default: () => '',
  },
});

const emit = defineEmits(['update:modelValue'])

let oldHtml = ''

let wangEditorE = null; // 创建编辑器实例的变量
// 创建实例，图片上传，各种配置项
const wangEditorFun = () => {
  const wangEditorDOM = document.getElementById(`wang-editor-${props.field}`);
  if (!wangEditorDOM) return;
  wangEditorE = new WangEditor(wangEditorDOM); // 初始化编辑器实例
  // 菜单
  wangEditorE.config.menus = [
    'bold',
    'head',
    'link',
    'italic',
    'underline',
    'image'
  ]
  // 配置 onchange 回调函数
  wangEditorE.config.onchange = function (newHtml) {
    if(newHtml.length > props.contentMaxLength) {
      ElMessage.warning(`富文本总长度不能超过${props.contentMaxLength}`)
      wangEditorE.txt.html(oldHtml);
      return
    }
    const safeHtml = xss(newHtml)
    oldHtml = safeHtml
    // 通知父组件更新
    emit('update:modelValue', safeHtml)
  }
  wangEditorE.config.uploadImgMaxSize = 512 * 1024 * 1024 // 限制为512MB
  wangEditorE.config.uploadImgAccept = ['png', 'jpg', 'jpeg']
  wangEditorE.config.uploadImgMaxLength = 1
  wangEditorE.config.customUploadImg = async function (resultFiles, insertImgFn) {
      resultFiles.forEach((file) => {
        const suffix = file.name.split('.').pop().toLowerCase()
        try {
          const response = getAuthCode({
            filePostfix: suffix,
            scene: 'VOC_UPLOAD_ATTACHMENT'
          }).then(response => {
            const uploadParams = {
              fileName: file.name,
              fileCount: 1,
              authCode: response,
              name: file.name,
              file: file,
            }
            upload(uploadParams).then((response) => {
              insertImgFn(response)
            })
          })   
        } catch (err) {
          ElMessage.error('上传失败，请稍后再试')
        }
      })
  }
  oldHtml = props.modelValue
  wangEditorE.create(); // 创建编辑器
  wangEditorE.txt.html(
    props.modelValue
  );
};

onMounted(() => {
  wangEditorFun();
});

</script>
<style lang="scss" scoped>
[id^="wang-editor"] {
  z-index: 0 !important;
  width: 100%;
  :deep(.w-e-toolbar) {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  :deep(.w-e-text-container) {
    min-height: 100px !important;
    height: 100% !important;
    max-height: 300px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    .w-e-text {
      min-height: 100px !important;
      max-height: 300px;
    }
  }
}
</style>
