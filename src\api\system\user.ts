import { ewpService as service } from '@/utils/axios';

// 登录接口
export function login(data: { account: string; password: string }) {
  return service.post('/user/login', data);
}

export function logout() {
  return service.post('/user/logout');
}

export function findUserResource() {
  return service.get('/user/queryUserResource');
}


export function tokenAuth() {
  return service.get('/user/getLoginStatus');
}


export function findUserSimpleInfoByToken() {
  return service.get('/user/getUserByToken');
}


export function queryRoleListByToken() {
  return service.get('/user/queryRoleList');
}

