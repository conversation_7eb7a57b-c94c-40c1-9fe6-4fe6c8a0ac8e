<script lang="ts" setup>
import { <PERSON>t<PERSON>ottom, CaretTop } from "@element-plus/icons-vue";
import { cloneDeep } from "lodash-es";
import { reactive } from "vue";
import { ref } from "vue";
import dayjs from "dayjs";
import {
  defaultFormData,
  FormComponentsData,
  defaulList,
  otherList,
  ewpOwnerOption,
  getLevels,
  getTypes,
  getEwpOwner,
  getTestOwner,
} from "./index";
import { onMounted } from "vue";
import searchArea from "@/components/issuesOpinionManage/commonArea/serachArea.vue";
import { ewpService as service } from "@/utils/axios";
import { ElMessage, UploadUserFile } from "element-plus";
import { LocalStorageEnum } from "@/components/columnSettingsDialog/index";
import { storage } from "@/utils/Storage";
import ColumnSettingDialog from "@/components/columnSettingsDialog/index.vue";
import { pageSizes } from "@/utils/constant";
import { isDev, WO_AUTH } from "@/utils/env";
import { csrfTokenManager } from "@/utils/CSRFTokenManager";

const filterObj = ref({
  appName: "",
  company: "",
  ewpOwner: "",
  testOwner: "",
});

const pageInfo = ref({
  pageNo: 1,
  pageSize: 10,
});

const tableData = ref([]);
const pageNum = ref(0);
const importing = ref(false);
// 全部列与默认列一样
const allColumn = [
  { prop: "appName", label: "应用名称", width: "200" },
  { prop: "appShape", label: "应用形态", width: "130" },
  { prop: "company", label: "应用归属公司", width: "200" },
  { prop: "ewpOwner", label: "问题保障责任人", width: "140" },
  { prop: "ewpLeader", label: "问题保障 TL", width: "140" },
  { prop: "testOwner", label: "问题测试责任人", width: "140" },
  { prop: "appBundle", label: "应用包名", width: "110" },
  { prop: "appType", label: "应用垂类", width: "110" },
  { prop: "priority", label: "标签", width: "110" },
  {
    prop: "dtseOwner",
    label: "DTSE责任人",
    width: "110",
    clickHandle: (row) => {
      openIMChatClick(row.dtseOwner);
    },
  },
  {
    prop: "dtseLeader",
    label: "DTSE TL",
    width: "110",
    clickHandle: (row) => {
      openIMChatClick(row.dtseLeader);
    },
  },
  {
    prop: "solutionOwner",
    label: "生态解决方案责任人",
    width: "150",
    clickHandle: (row) => {
      openIMChatClick(row.solutionOwner);
    },
  },
  {
    prop: "solutionLeader",
    label: "生态解决方案TL",
    width: "120",
    clickHandle: (row) => {
      openIMChatClick(row.solutionLeader);
    },
  },
  {
    prop: "acceptanceOwner",
    label: "体验测试责任人",
    width: "120",
    clickHandle: (row) => {
      openIMChatClick(row.acceptanceOwner);
    },
  },
  {
    prop: "acceptanceLeader",
    label: "体验测试TL",
    width: "110",
    clickHandle: (row) => {
      openIMChatClick(row.acceptanceLeader);
    },
  },
  {
    prop: "bdOwner",
    label: "BD责任人",
    width: "110",
    clickHandle: (row) => {
      openIMChatClick(row.bdOwner);
    },
  },
  {
    prop: "bdLeader",
    label: "BD TL",
    width: "110",
    clickHandle: (row) => {
      openIMChatClick(row.bdLeader);
    },
  },
  {
    prop: "appPM",
    label: "应用PM",
    width: "110",
    clickHandle: (row) => {
      openIMChatClick(row.appPM);
    },
  },
  {
    prop: "viewer",
    label: "应用查看员",
    width: "110",
  },
  {
    prop: "appId",
    label: "应用id",
    width: "110",
    renderStr: (row) => {
      return row?.appPublishInfoPo?.appId;
    },
  },
  { prop: "represent", label: "应用归属省份", width: "110" },
];

const columnList = ref([]);
const setColumn = (columnData) => {
  columnList.value = [
    { prop: "checkbox", label: "", width: "40px", type: "selection" },
    ...cloneDeep(columnData),
  ];
};
const fileList = ref<UploadUserFile[]>([]);
const actionUrl = `${import.meta.env.VITE_APP_BASE_API}/app-info/importMember`;
const headersConfig = {};
const getHeaderConfigs = () => {
  // 使用新的 CSRFTokenManager 获取 CSRF 令牌
  const csrfToken = csrfTokenManager.getToken();
  if (csrfToken) {
    headersConfig["X-CSRF-TOKEN"] = csrfToken;
  }
  if (isDev()) {
    headersConfig.token = atob(WO_AUTH);
  }
};
getHeaderConfigs();

const uploadSuccess = (response) => {
  ElMessage({
    type: "success",
    message: "导入成功！",
  });
  getDataList();
};

const uploadError = (e) => {
  ElMessage({
    type: "error",
    message: "导入失败！",
  });
};

const getDefaultFilterOptionVals = () => {
  const res = {};
  defaulList.concat(otherList).forEach((item) => {
    res[item.key] = ["all"];
  });
  return res;
};

const filterOptions = ref(getDefaultFilterOptionVals());
const handleFilterOptions = (value) => {
  filterOptions.value = value;
  getDataList();
};

const getFilterParams = () => {
  const res = {};
  Object.keys(filterOptions.value).forEach((key) => {
    if (!filterOptions.value[key].includes("all")) {
      res[key] = filterOptions.value[key].join(",");
    }
  });
  return res;
};

const handleSizeChange = (num) => {
  pageInfo.value.pageSize = num;
  getDataList();
};

const handleCurrentChange = (num) => {
  pageInfo.value.pageNo = num;
  getDataList();
};

// 查询表格数据
const getDataList = async () => {
  try {
    const data = {
      pageNo: pageInfo.value.pageNo,
      pageSize: pageInfo.value.pageSize,
      ...getFilterParams(),
      appName: filterObj.value.appName,
      ewpOwner: filterObj.value.ewpOwner,
      company: filterObj.value.company,
      testOwner: filterObj.value.testOwner,
    };
    const res = await service.post(`/app-info/apps`, data);
    tableData.value = res.records || [];
    pageNum.value = res.total;
  } catch (error) {
    console.error("Error fetching work order list:", error);
    throw error;
  }
};

// 查询重置
const reset = () => {
  filterObj.value = {
    appName: "",
    company: "",
    ewpOwner: "",
    testOwner: "",
  };
  pageInfo.value = {
    pageNo: 1,
    pageSize: 10,
  };
  filterOptions.value = getDefaultFilterOptionVals();
  getDataList();
};

const showAllInputFilter = ref(false);
const formRef = ref();
const showDialog = ref(false);
const dialogTitle = ref("新增");

let formData = reactive(cloneDeep(defaultFormData));

// 多选数据从逗号拼接转成数组
const convertMultipleData = (formComponents, formData) => {
  formComponents.forEach((item) => {
    if (item.component === "Select" && item.multiple) {
      formData[item.key] = formData[item.key]?.split(",");
    }
  });
};

// 新增弹窗
const addApp = () => {
  console.log(formRef.value, "formRef.value");
  formRef.value && formRef.value.resetFields();
  formData = reactive(cloneDeep(defaultFormData));
  dialogTitle.value = "新增";
  showDialog.value = true;
};

// 编辑弹窗
const editApp = (scope) => {
  formRef.value && formRef.value.resetFields();
  formData = reactive(cloneDeep(scope.row));
  convertMultipleData(FormComponentsData, formData);
  dialogTitle.value = "编辑";
  showDialog.value = true;
};

// 删除
const delApp = async (scope) => {
  try {
    await service.post(`/app-info/app/${scope.row.id}`);
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    getDataList();
  } catch (error) {
    ElMessage({
      type: "error",
      message: "删除失败",
    });
  }
  console.log("删除应用");
};

// 处理多选框数据
const progressMultipleData = (rawMap) => {
  const result = {};
  Object.keys(rawMap).forEach((item) => {
    result[item] = rawMap[item]?.toString();
  });
  return result;
};

// 确定按钮
const onSubmit = () => {
  if (!formRef.value) return;
  formRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        const progressedMultipleData = progressMultipleData({
          priority: formData.priority,
          ewpOwner: formData.ewpOwner,
          ewpLeader: formData.ewpLeader,
          testOwner: formData.testOwner,
        });
        await service.post("/app-info/app", {
          ...formData,
          ...progressedMultipleData,
        });
        showDialog.value = false;
        getDataList();
        ElMessage({
          type: "success",
          message: `${dialogTitle.value}成功`,
        });
      } catch (error) {
        ElMessage({
          type: "error",
          message: `${error.msg}`,
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

function initESpace() {
  eSpaceCtrl.init();
  eSpaceCtrl.ready((data) => {
    console.log("link success");
    //这里做一些加载时做的操作，比如是否让之后的openIMChatClick()可以调用
  });
  eSpaceCtrl.error(() => {
    console.log("link error");
  });
  eSpaceCtrl.on("user-status-change", (status) => {
    // @TODO监听用户的变化
  });
}

function openIMChatClick(expId) {
  let expIds = expId.split(",");
  expIds.forEach((id) => {
    eSpaceCtrl.showImDialog(id, (error) => {});
  });
}

onMounted(() => {
  initESpace();
  getDataList();
  getLevels();
  getTypes();
  getEwpOwner();
  getTestOwner();
  // 设置动态列
  const localStorageClounm = storage.get(LocalStorageEnum.MetaManager);
  if (localStorageClounm) {
    setColumn(localStorageClounm);
  } else {
    setColumn(allColumn);
  }
});
</script>

<template>
  <div class="page-title-section mt-12">
    <div class="page-title">元数据管理</div>
  </div>
  <div class="filter-comp">
    <searchArea
      :defaulList="defaulList"
      :otherList="otherList"
      :filterOptions="filterOptions"
      @updateFilterOptions="handleFilterOptions"
    ></searchArea>
    <div class="table-head">
      <div class="table-header-row">
        <div class="table-head-left">
          <el-input
            v-model="filterObj.appName"
            placeholder="请输入应用名称"
            class="filter-item"
          />
          <el-input
            v-model="filterObj.company"
            placeholder="请输入应用归属公司"
            class="filter-item"
            style="margin-left: 10px"
          />
          <el-button
            type="primary"
            text
            @click="showAllInputFilter = !showAllInputFilter"
            style="margin-left: 8px"
          >
            <span style="width: 50px">{{
              !showAllInputFilter ? "更多搜索" : "收起"
            }}</span>
            <el-icon class="el-icon--right" v-show="!showAllInputFilter"
              ><CaretBottom
            /></el-icon>
            <el-icon class="el-icon--right" v-show="showAllInputFilter"
              ><CaretTop
            /></el-icon>
          </el-button>
          <el-button
            type="primary"
            @click="getDataList"
            style="margin-left: 8px"
            >查询</el-button
          >
          <el-button @click="reset">重置</el-button>
        </div>
        <div class="flex-box">
          <el-button type="primary" @click="addApp">新增</el-button>
          <el-upload
            :show-file-list="false"
            accept=".xlsx, .xls"
            :action="actionUrl"
            :headers="headersConfig"
            :on-success="uploadSuccess"
            :on-error="uploadError"
            style="margin: 0 10px"
          >
            <el-button secondary>
              {{ "批量导入" }}
            </el-button>
          </el-upload>
          <ColumnSettingDialog
            :allColumn="allColumn"
            :defaultColumn="allColumn"
            :localStorageName="LocalStorageEnum.MetaManager"
            @saveHandle="setColumn"
          />
        </div>
      </div>

      <div v-if="showAllInputFilter" class="flex-box table-header-row">
        <el-form-item>
          <div class="flex-box ml-8">
            <el-select
              placeholder="请选择问题保障责任人"
              class="filter-item"
              v-model="filterObj.ewpOwner"
              clearable
              filterable
            >
              <el-option
                v-for="item in ewpOwnerOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="flex-box ml-8" style="margin-left: 10px">
            <el-select
              placeholder="请选择问题测试责任人"
              v-model="filterObj.testOwner"
              class="filter-item"
              clearable
              filterable
            >
              <el-option
                v-for="item in ewpOwnerOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </el-form-item>
      </div>
    </div>
  </div>
  <div class="search-result-table">
    <div class="table-wrap">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column
          v-for="item in columnList"
          :key="item.prop"
          :type="item.type"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          show-overflow-tooltip
        >
          <template #default="scope" v-if="item.prop === 'appName'">
            <div class="flex-box a-i-center" style="height: 56px">
              <el-badge :value="scope.row.top" :offset="[-24, 0]">
                <el-tag type="primary" size="large">
                  {{ scope.row.appName }}
                </el-tag>
              </el-badge>
            </div>
          </template>
          <template #default="scope" v-else-if="item.prop !== 'checkbox'">
            <span
              :class="item.clickHandle ? 'primary-font' : ''"
              @click="
                () => {
                  item.clickHandle && item.clickHandle(scope.row);
                }
              "
            >
              {{
                (item.renderStr && item.renderStr(scope.row)) ||
                scope.row[item.prop] ||
                "---"
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="config" label="操作" width="120" fixed="right">
          <template #default="scope">
            <span class="primary-font" @click="editApp(scope)">编辑</span>
            <span
              @click="delApp(scope)"
              class="primary-font"
              style="margin-left: 16px"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-box j-c-space-between a-i-center mt-12">
        <span class="pagination-total">{{ `总计：${pageNum}` }}</span>
        <div class="flex-box a-i-center">
          <el-pagination
            background
            v-model:current-page="pageInfo.pageNo"
            layout="prev, pager, next, sizes, jumper"
            :total="pageNum"
            :page-sizes="pageSizes"
            :page-size="pageInfo.pageSize"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
  <el-dialog :title="dialogTitle + '应用'" width="1300" v-model="showDialog">
    <el-form
      :model="formData"
      label-width="150"
      label-position="left"
      ref="formRef"
    >
      <el-row :gutter="60">
        <el-col v-for="item in FormComponentsData" :key="item.key" :span="8">
          <el-form-item
            :label="item.label"
            :prop="item.key"
            :rules="item.rules"
            :class="item.rules?.required ? '' : 'unRequired'"
          >
            <el-input
              v-if="item.component === 'Input'"
              placeholder="请输入"
              v-model="formData[item.key]"
            />
            <el-select
              v-if="item.component === 'Select'"
              placeholder="请选择"
              v-model="formData[item.key]"
              :multiple="item.multiple"
              clearable
              filterable
            >
              <el-option
                v-for="o in item.option"
                :key="o.value"
                :label="o.label"
                :value="o.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="text-align: end">
      <el-button @click="showDialog = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.table-head {
  display: flex;
  justify-content: space-between;
}
.table-head-left {
  display: flex;
}
.el-form-item {
  margin-bottom: 24px;
}
</style>
