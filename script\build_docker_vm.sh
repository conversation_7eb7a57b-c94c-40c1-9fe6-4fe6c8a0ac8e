#!/bin/bash

set -ex
set -o pipefail

BASE_PATH=$(cd $(dirname $0);pwd)
PROJECT_PATH=$(cd $BASE_PATH/..;pwd)

SERVICE_NAME=$(awk -F "=" '/service_name/ {print $2}' "${BASE_PATH}"/service.ini)

cd $PROJECT_PATH
PACKAGE_VERSION=$(npm pkg get version|tr -d '"')
SUB_VERSION=${ENV_PIPELINE_STARTTIME}
PACKAGE_VERSION="${PACKAGE_VERSION}.${SUB_VERSION}"

function build_portal_package() {
  temp_path=${WORKSPACE}/codetemp
  mkdir -p ${temp_path}
  cd ${temp_path}

  PACKAGE_NAME="${SERVICE_NAME}_Iac_${PACKAGE_VERSION}(MCOM).zip"
#  unzip -q ${PACKAGE_NAME}
  #解压业务包
#  unzip -q "${SERVICE_NAME}_${PACKAGE_VERSION}.zip"
  #根据业务实际需要修改后者路径
#  mv ${temp_path}/dist ${PROJECT_PATH}/src/main/webapp/portal/
}

function build_sh_package() {
  # 替换启动脚本
  cd ${PROJECT_PATH}
  rm -f deploy/bin/install.sh deploy/bin/start.sh deploy/bin/stop.sh
  # 保留start.sh 及 stop.sh(优雅下线参数必须)
  mv deploy/bin/start_runtime.sh deploy/bin/start.sh
  mv deploy/bin/stop_runtime.sh deploy/bin/stop.sh
}

function build_app_package() {
  cd ${PROJECT_PATH}
#  mvn clean package -Duser.timezone=GMT+8 -Dmaven.test.skip=true -Dpackage.version=${PACKAGE_VERSION}
}

function main() {
#  build_portal_package
  build_sh_package
  build_app_package
}

main