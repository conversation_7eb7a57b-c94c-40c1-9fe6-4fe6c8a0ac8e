import { ewpService as service } from "@/utils/axios";

export async function queryByPage(params: any): Promise<any> {
  try {
    const response = await service.post(`/clusterScene/queryByPage`, params);
    return response;
  } catch (error) {
    throw error;
  }
}

// 创建问题
export async function createOpinionOrder(params: any): Promise<any> {
  try {
    const response = await service.post(`/opinionIssue/createOpinionIssue`, params);
    return response;
  } catch (error) {
    throw error;
  }
}

// 导入源声
export async function createOriginData(params: any): Promise<any> {
  try {
    const response = await service.post(`/manualOriginData/createOriginData`, params);
    return response;
  } catch (error) {
    throw error;
  }
}

// 导入源声，获取场景分类编码
export async function getSceneIdBySceneName({ sceneName, originVolume, appName }) {
  try {
    const response = await service.post(
      `/manualOriginData/generateSceneId?sceneName=${sceneName}&originVolume=${originVolume}&appName=${appName}`
    );
    return response;
  } catch (error) {
    throw error;
  }
};

// 获取场景源声详情
export async function getOriginVolumeDetailById(data) {
  try {
    const response = await service.post(`/wiseoperOriginData/queryBySceneId`, data);
    return response;
  } catch (error) {
    throw error;
  }
}

// 获取场景源声详情——导入状态
export async function getOriginVolumeExportDetailById(data) {
  try {
    const response = await service.post(`/wiseoperOriginData/querySyncLog`, data);
    return response;
  } catch (error) {
    throw error;
  }
}

// 获取场景源声详情——加工状态
export async function getOriginVolumeWorkDetailById(data) {
  try {
    const response = await service.post(`/clusterScene/queryClusterLog`, data);
    return response;
  } catch (error) {
    throw error;
  }
}

// 根据应用名称获取问题处理人
export async function getTestOwnerByappName(data) {
  try {
    const response = await service.post(`/app-info/app/getAppTestOwner`, data);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * 更新源声
 */
export const updateSourceScene = async (params) => {
  try {
    const response = await service.post(`/manualOriginData/updateOriginData`, params);
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * 删除源声
 */
export const delSourceScene = async (params) => {
  try {
    const response = await service.post(`/manualOriginData/deleteOriginData/${params.id}`);
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * 更新场景
 */
export const updateClusterScene = async (params) => {
  try {
    const response = await service.post(`/clusterScene/update`, params);
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * 提交申请场景屏蔽
 */
export const fetchBlockScene = async (params) => {
  try {
    const response = await service.post(`/clusterScene/blockScene`, params);
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * 输入场景名称，联想功能
 */
export const fetchSceneNameAutocomplete = async (params) => {
  try {
    const response = await service.get(
      `/clusterScene/searchByKeyword?appName=${params.appName}&keyword=${params.keyword}&limit=${params.limit}`
    );
    return response;
  } catch (error) {
    throw error;
  }
};
