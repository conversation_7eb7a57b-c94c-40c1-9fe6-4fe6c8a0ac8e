import { ewpService as service } from '@/utils/axios';

export const problemBelongingsOptions = [
  {
    label: '生态伙伴',
    value: '生态伙伴'
  },
  {
    label: 'OS系统',
    value: 'OS系统'
  },
  {
    label: '非问题',
    value: '非问题'
  },
  {
    label: '卓易通',
    value: '卓易通'
  }
]

export const operationTypeOptions = [
  {
    value: '0000',
    label: '转单',
  },
  {
    value: '0001',
    label: '完成',
  },
  {
    value: '0002',
    label: '驳回',
  },
  {
    value: '0010',
    label: '待观察',
  },
  {
    value: '1000',
    label: '非问题',
  }
]

// 保存定界过程
export const saveDelimitProblem = async ({weknowId='',relateWorkOrders, classification, faultKnowledgeID, problemBelongings, delimitProgress, conclusion, modifySuggestion, opinionIssueId, attachmentItems }) => {
  const params = {
    opinionIssueId,
    faultCategory: classification,
    faultKnowledgeId: faultKnowledgeID,
    issueAttribution: problemBelongings,
    delimitationProcess: delimitProgress,
    analysisConclusion: conclusion,
    amendAdvice: modifySuggestion,
    relateWorkOrders: relateWorkOrders,
    weknowId,
    attachmentItems,
  }

  try {
    const response = await service.post('/issueDelimitation/save', params, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return response;
  } catch (error) {
      throw error;
  }
}

// 完成/转单
export const submitDelimitProblem = async ({weknowId,relateWorkOrders, classification, faultKnowledgeID, problemBelongings, delimitProgress, conclusion, modifySuggestion, operationType, nextHandler, opinionIssueId, attachmentItems,reason }) => {
  const params = {
    opinionIssueId,
    weknowId,
    faultCategory: classification,
    faultKnowledgeId: faultKnowledgeID,
    issueAttribution: problemBelongings,
    delimitationProcess: delimitProgress,
    analysisConclusion: conclusion,
    amendAdvice: modifySuggestion,
    operationType: operationType,
    nextHandler: nextHandler,
    relateWorkOrders: relateWorkOrders,
    attachmentItems,
    reason
  }
  try {
    await service.post('/issueDelimitation/submit', params, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
  } catch (error) {
      throw error;
  }
}

// 保存/更新复现设备信息
export const saveReproductionDeviceInfo = async ({ 
  id, 
  opinionIssueId, 
  deviceType, 
  deviceModel, 
  probability, 
  appVersion, 
  osVersion 
}) => {
  const params = {
    id,
    opinionIssueId,
    deviceType,
    deviceModel,
    probability,
    appVersion,
    osVersion
  }
  try {
    const response = await service.post('/issueReproduction/insertOrUpdate', params, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 查询复现设备信息
export const queryReproductionDevices = async (opinionIssueId) => {
  try {
    const response = await service.get(`/reproductionDevice/query?opinionIssueId=${opinionIssueId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 查询接口返回值
export const queryStepInfo = async (opinionIssueId) => {
  try {
    const response = await service.get(`issueReproduction/queryMaxVersionData?opinionIssueId=${opinionIssueId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 删除复现设备信息
export const deleteReproductionDevice = async (id) => {
  try {
    const response = await service.get(`/reproductionDevice/delete?id=${id}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 新增复现设备信息
export const insertReproductionDevice = async ({ 
  opinionIssueId, 
  deviceType, 
  deviceModel, 
  probability,
  reproductionPath,
  appVersion, 
  osVersion
}) => {
  const params = {
    opinionIssueId,
    deviceType,
    deviceModel,
    probability,
    reproductionPath,
    appVersion,
    osVersion
  }
  try {
    const response = await service.post('/reproductionDevice/insert', params, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 更新复现设备信息
export const updateReproductionDevice = async ({ 
  id,
  opinionIssueId, 
  deviceType, 
  deviceModel, 
  probability,
  reproductionPath,
  appVersion, 
  osVersion
}) => {
  const params = {
    id,
    opinionIssueId,
    deviceType,
    deviceModel,
    probability,
    reproductionPath,
    appVersion,
    osVersion
  }
  try {
    const response = await service.post('/reproductionDevice/update', params, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 保存复现问题状态
export const saveReproduction = async ({
    opinionIssueId,
    opinionIssueLevel,
    severity,
    faultCategory,
    childIssue,
    description,
    operateType,
    reproductionDeviceInfoList
  }) => {
  const params = {
    opinionIssueId,
    opinionIssueLevel,
    severity,
    faultCategory,
    childIssue,
    description,
    updatePerson: '', // 可以从用户信息中获取，或者后端自动填充
    operateType,
    reproductionDeviceInfoList
  }
  try {
    const response = await service.post('/issueReproduction/insertOrUpdate', params, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// 提交复现问题状态
export const submitIssueReproduction= async ({
  opinionIssueId,
  opinionIssueLevel,
  severity,
  faultCategory,
  childIssue,
  description,
  operateType,
  reason,
  assignedPerson,
  reproductionDeviceInfoList,
  attachmentItems
}) => {
  const params = {
    opinionIssueId,
    opinionIssueLevel,
    severity,
    faultCategory,
    reason,
    childIssue,
    description,
    operateType,
    assignedPerson,
    reproductionDeviceInfoList,
    attachmentItems
  }
  try {
    await service.post('/issueReproduction/submit', params, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    throw error;
  }
}

// 获取表单数据
export const fetchDeLmitProblemData = async (opinionIssueId) => {
  try {
    const response = await service.get(`/issueDelimitation/view?opinionIssueId=${opinionIssueId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    console.log('response:', response)
    if (!response.issueDelimitation) {
      response.issueDelimitation = {};
    }
    const {
      issueDelimitation,
      relateWorkOrders,
      attachmentInfos,
    } = response;
    const {
      faultCategory,
      faultKnowledgeId,
      issueAttribution,
      delimitationProcess,
      analysisConclusion,
      amendAdvice,
      operationType,
      nextHandler,
      weknowId,
    } = issueDelimitation;
    return {
      classification: faultCategory,
      faultKnowledgeID: faultKnowledgeId,
      problemBelongings: issueAttribution,
      delimitProgress: delimitationProcess,
      conclusion: analysisConclusion,
      modifySuggestion: amendAdvice,
      relateWorkOrders,
      operationType,
      nextHandler,
      weknowId,
      attachmentInfos
    };
  } catch (error) {
      throw error;
  }
}
