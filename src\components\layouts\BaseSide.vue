<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  HomeFilled,
  UserFilled,
  Document,
  Setting,
  Folder
} from '@element-plus/icons-vue'

const router = useRouter()
const activeIndex = ref('dashboard')

const handleSelect = (key: string) => {
  activeIndex.value = key
  
  // 根据菜单key跳转到对应路由
  switch (key) {
    case 'dashboard':
      router.push('/dashboard')
      break
    case 'user-list':
      router.push('/user/list')
      break
    case 'user-roles':
      router.push('/user/roles')
      break
    case 'user-permissions':
      router.push('/user/permissions')
      break
    default:
    // 其他路由...
  }
}
</script>

<template>
  <div class="sidebar-container">
    <el-menu
      :default-active="activeIndex"
      class="sidebar-menu"
      @select="handleSelect"
      background-color="#001529"
      text-color="#fff"
      active-text-color="#409EFF"
    >
      <el-menu-item index="dashboard">
        <el-icon><HomeFilled /></el-icon>
        <span>仪表盘</span>
      </el-menu-item>
      
      <el-sub-menu index="user">
        <template #title>
          <el-icon><UserFilled /></el-icon>
          <span>用户管理</span>
        </template>
        <el-menu-item index="user-list">用户列表</el-menu-item>
        <el-menu-item index="user-roles">角色管理</el-menu-item>
        <el-menu-item index="user-permissions">权限管理</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="content">
        <template #title>
          <el-icon><Document /></el-icon>
          <span>内容管理</span>
        </template>
        <el-menu-item index="content-articles">文章列表</el-menu-item>
        <el-menu-item index="content-categories">分类管理</el-menu-item>
        <el-menu-item index="content-tags">标签管理</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="settings">
        <template #title>
          <el-icon><Setting /></el-icon>
          <span>系统设置</span>
        </template>
        <el-menu-item index="settings-basic">基本设置</el-menu-item>
        <el-menu-item index="settings-security">安全设置</el-menu-item>
        <el-menu-item index="settings-logs">系统日志</el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<style scoped lang="scss">
.sidebar-container {
  width: 220px;
  height: 100%;
  background-color: #001529;
  
  .sidebar-menu {
    height: 100%;
    border-right: none;
  }
}

:deep(.dark) {
  .sidebar-container {
    background-color: #1f1f1f;
    
    .sidebar-menu {
      background-color: #1f1f1f;
    }
  }
}
</style>
