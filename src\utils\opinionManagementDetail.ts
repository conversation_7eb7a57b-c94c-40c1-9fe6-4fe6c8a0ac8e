/*
* 问题管理详情页
* */
import { ElMessage } from "element-plus";
import { queryOrderStatus } from "@/api/view/optionDetail/detail";
import { useConfigStore } from "@/store/modules/config";

const configStore = useConfigStore();
// 跳转关联问题单
export const goRelatedOrder = async (data: any, key: string, type: string) => {
    const orderId: string | undefined = data?.[key];
    const orderType: string | undefined = data?.[type];
    if (!orderId || !orderType) {
        ElMessage({
            type: "error",
            message: "请完成关联单类型和关联单号的填写",
        });
        return;
    }

    const url = buildRelatedOrderUrl(orderType, orderId);
    if (!url) {
        ElMessage({
            type: "error",
            message: "未找到关联单的路径",
        });
        return;
    }
    openInNewTab(url);
}

function openInNewTab(url: string): void {
    try {
        const anchor = document.createElement("a");
        anchor.href = url;
        anchor.target = "_blank";
        anchor.rel = "noopener noreferrer";
        document.body.appendChild(anchor);
        anchor.click();
        document.body.removeChild(anchor);
    } catch (err) {
        const newWindow = window.open(url, "_blank", "noopener");
        if (newWindow) {
            newWindow.opener = null;
        } else {
            window.location.href = url;
        }
    }
}

function buildRelatedOrderUrl(orderType: string, orderId: string): string | null {
    const upperType = (orderType || "").toUpperCase();
    let tempType = upperType === 'TALM单' ? 'TALM' : upperType;
    const safeId = encodeURIComponent(orderId || "");
    const urls = configStore.getTempOrderUrls;
    if (!urls[tempType]) {
        return null;
    }
    switch (tempType) {
        case "DTS":
            return urls[tempType].replace('${No}', safeId);
        case "IR":
            return urls[tempType].replace('${No}', safeId);
        case "IONE": {
            if (/^SR/i.test(orderId)) {
                return urls[tempType].replace('${type}', 'Sr').replace('${No}', safeId);
            }
            if (/^AR/i.test(orderId)) {
                return urls[tempType].replace('${type}', 'Ar').replace('${No}', safeId);
            }
            return urls[tempType].replace('${type}', 'Sr').replace('${No}', safeId);
        }
        case "TALM": {
            const type = orderId.substring(0, 2);
            return urls[tempType].replace('${type}', type);
        }
        default:
            return null;
    }
}

// 查询问题单状态
export const updateOrderStatus = async (type, orderNo, order) => {
    order.orderStatus = "";
    if (type && orderNo) {
        if (validateOrderUtil( { relatedOrderType: type, relatedOrderId: orderNo })) {
          const status = await queryOrderStatus(type, orderNo);
          status && (order.orderStatus = status);
        }
    }
}

export const validateOrderUtil = (oneRow) => {
    const { relatedOrderType, relatedOrderId } = oneRow;
    if (!relatedOrderType) {
      ElMessage({
        type: "error",
        message: "请输入关联单类型",
      });
      return false;
    }
    if (!relatedOrderId) {
      ElMessage({
        type: "error",
        message: "请输入关联单号",
      });
      return false;
    }
    if (relatedOrderType === "DTS") {
      if (/^DTS\d{13}$/.test(relatedOrderId)) {
        return true;
      } else {
        ElMessage({
          type: "error",
          message: "请输入正确的DTS单号",
        });
        return false;
      }
    } else if (relatedOrderType === "IR") {
      if (/^\d{15}$/.test(relatedOrderId)) {
        return true;
      } else {
        ElMessage({
          type: "error",
          message: "请输入正确的IR单号",
        });
        return false;
      }
    } else if (relatedOrderType === "TALM") {
      if (
        /^AR\d{14}$/.test(relatedOrderId) ||
        /^SR\d{14}$/.test(relatedOrderId)
      ) {
        return true;
      } else {
        ElMessage({
          type: "error",
          message: "请输入正确的TALM单号",
        });
        return false;
      }
    } else if (relatedOrderType === "IONE") {
      if (/^SR/i.test(relatedOrderId) || /^AR/i.test(relatedOrderId)) {
        return true;
      } else {
        ElMessage({
          type: "error",
          message: "请输入正确的IONE单号",
        });
        return false;
      }
    }
  };
