import { ewpService as service } from '@/utils/axios';

/**
 * 根据场景ID查询声量数据
 * @param params 查询参数
 * @returns Promise<any>
 */
export async function queryVolumeBySceneId(params: {
  sceneId: string;
  startTime: string;
  endTime: string;
}): Promise<any> {
  try {
    const response = await service.post('/wiseoperOriginData/queryVolumeBySceneId', params);
    return response;
  } catch (error) {
    console.error('查询声量数据失败:', error);
    throw error;
  }
}
