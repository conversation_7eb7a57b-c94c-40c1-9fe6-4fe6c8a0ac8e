
import dayjs from "dayjs";
// 转换时间

export const transTime = (val) => {
    return val ? dayjs(val).format("YYYY-MM-DD") : "---";
};

export const string2Array = (value, symbolVal) => {
    if (typeof value === "string") {
        return value.split(symbolVal).filter(item => item)
    } else if (Array.isArray(value)) {
        return value
    }
}

export const array2String = (value, symbolVal) => {
    if (typeof value === "string") {
        return value
    } else if (Array.isArray(value)) {
        return value.join(symbolVal)
    }
}
