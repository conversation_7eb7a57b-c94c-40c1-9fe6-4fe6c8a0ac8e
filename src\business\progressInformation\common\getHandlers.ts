import { ewpService as service } from '@/utils/axios';

const progressHandlersOptions = (handlers) => {
  return handlers.map(item => {
    return {
      label: item,
      value: item,
    }
  })
}

export const getHandlers = async (opinionIssueId, operationType) => {
  return await service.get(`/progressInfo/getHandlers?opinionIssueId=${opinionIssueId}&operationType=${operationType}`, {
    headers: {
        'Content-Type': 'application/json',
    },
  }).then((data) => {
    if(data.handlers?.length === 0) {
      data.handlers.push(data.defaultHandler)
    }
    data.handlers = progressHandlersOptions(data.handlers)
    return data;
  });
}

export const needGetHandlers = (operationType) => {
  return operationType === '0000' || operationType === '0001' || operationType === '0002'
}