<?xml version="1.0" encoding="UTF-8"?><!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
  -->

<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
    <id>make-auto-deploy</id>

    <includeBaseDirectory>false</includeBaseDirectory>

    <formats>
        <format>zip</format>
    </formats>

    <dependencySets>
        <dependencySet>
            <outputDirectory>service/libs</outputDirectory>
            <scope>runtime</scope>
            <useTransitiveFiltering>true</useTransitiveFiltering>
        </dependencySet>
    </dependencySets>

    <fileSets>
        <fileSet>
            <directory>src/main/config</directory>
            <outputDirectory>service/config</outputDirectory>
            <fileMode>0600</fileMode>
            <directoryMode>0700</directoryMode>
            <lineEnding>unix</lineEnding>
            <excludes>
                <exclude>dos/opensupportupload.json</exclude>
                <exclude>sts/sts.properties</exclude>
                <exclude>application.properties</exclude>
                <exclude>devfs.properties</exclude>
                <exclude>fsenv.properties</exclude>
                <exclude>microservice.yaml</exclude>
                <exclude>nuwa.environment.config.properties</exclude>
                <exclude>sensitive.config.properties</exclude>
                <exclude>fs.scene.config.json</exclude>
            </excludes>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/src/main/webapp/portal</directory>
            <outputDirectory>service/portal</outputDirectory>
            <fileMode>0600</fileMode>
            <directoryMode>0700</directoryMode>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/src/main/webapp/dashboard</directory>
            <outputDirectory>service/dashboard</outputDirectory>
            <fileMode>0600</fileMode>
            <directoryMode>0700</directoryMode>
        </fileSet>
        <fileSet>
            <directory>deploy/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0700</fileMode>
            <directoryMode>0700</directoryMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>deploy</directory>
            <outputDirectory>./</outputDirectory>
            <includes>
                <include>package.json</include>
            </includes>
            <filtered>true</filtered>
        </fileSet>
        <fileSet>
            <directory>deploy/configtemplate</directory>
            <outputDirectory>configtemplate</outputDirectory>
            <fileMode>0600</fileMode>
            <directoryMode>0700</directoryMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
    </fileSets>
</assembly>