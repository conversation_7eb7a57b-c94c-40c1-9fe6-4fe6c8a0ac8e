/* 取消Element Plus组件的hover效果 */

/* 按钮hover效果取消 */
.ep-button:hover,
.ep-button:focus {
  color: var(--ep-button-text-color);
  border-color: var(--ep-button-border-color);
  background-color: var(--ep-button-bg-color);
  outline: none;
}

/* 主要按钮hover效果取消 */
.ep-button--primary:hover,
.ep-button--primary:focus {
  background-color: var(--ep-color-primary);
  border-color: var(--ep-color-primary);
  color: var(--ep-color-white);
}

/* 成功按钮hover效果取消 */
.ep-button--success:hover,
.ep-button--success:focus {
  background-color: var(--ep-color-success);
  border-color: var(--ep-color-success);
  color: var(--ep-color-white);
}

/* 警告按钮hover效果取消 */
.ep-button--warning:hover,
.ep-button--warning:focus {
  background-color: var(--ep-color-warning);
  border-color: var(--ep-color-warning);
  color: var(--ep-color-white);
}

/* 危险按钮hover效果取消 */
.ep-button--danger:hover,
.ep-button--danger:focus {
  background-color: var(--ep-color-danger);
  border-color: var(--ep-color-danger);
  color: var(--ep-color-white);
}

/* 信息按钮hover效果取消 */
.ep-button--info:hover,
.ep-button--info:focus {
  background-color: var(--ep-color-info);
  border-color: var(--ep-color-info);
  color: var(--ep-color-white);
}

/* 菜单项hover效果取消 */
.ep-menu-item:hover,
.ep-menu-item:focus,
.ep-menu--horizontal .ep-menu-item:not(.is-disabled):focus,
.ep-menu--horizontal .ep-menu-item:not(.is-disabled):hover {
  background-color: transparent !important;
  color: inherit;
}

/* 子菜单标题hover效果取消 */
.ep-sub-menu__title:hover,
.ep-sub-menu__title:focus {
  background-color: transparent !important;
  color: inherit;
}

/* 菜单项组hover效果取消 */
.ep-menu-item-group__title:hover,
.ep-menu-item-group__title:focus {
  background-color: transparent !important;
  color: inherit;
}

/* 输入框hover效果取消 */
.ep-input:hover .ep-input__wrapper,
.ep-input:focus .ep-input__wrapper,
.ep-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--ep-input-border-color) inset;
}

/* 标签hover效果取消 */
.ep-tag:hover {
  opacity: 1;
}

/* 下拉菜单项hover效果取消 */
.ep-dropdown-menu__item:not(.is-disabled):hover,
.ep-dropdown-menu__item:not(.is-disabled):focus {
  background-color: transparent;
  color: inherit;
}

/* 表格行hover效果取消 */
.ep-table__body tr.hover-row.current-row > td.ep-table__cell,
.ep-table__body tr.hover-row > td.ep-table__cell {
  background-color: inherit;
}

/* 选择器选项hover效果取消 */
.ep-select-dropdown__item.hover,
.ep-select-dropdown__item:hover {
  background-color: transparent;
}

/* 分页按钮hover效果取消 */
.ep-pagination .ep-pager li:hover {
  color: var(--ep-pagination-hover-color);
}

/* 日期选择器hover效果取消 */
.ep-date-table td.available:hover,
.ep-month-table td.available:hover,
.ep-year-table td.available:hover {
  color: inherit;
}

/* 树节点hover效果取消 */
.ep-tree-node:focus > .ep-tree-node__content,
.ep-tree-node__content:hover {
  background-color: transparent;
}

/* 开关hover效果取消 */
.ep-switch:hover:not(.is-disabled) .ep-switch__core {
  border-color: var(--ep-switch-border-color);
}

/* 单选框hover效果取消 */
.ep-radio:hover:not(.is-disabled) .ep-radio__inner {
  border-color: var(--ep-radio-input-border-color);
}

/* 复选框hover效果取消 */
.ep-checkbox:hover:not(.is-disabled) .ep-checkbox__inner {
  border-color: var(--ep-checkbox-input-border-color);
}

/* 弹出框hover效果取消 */
.ep-dialog__header:hover,
.ep-dialog__title:hover {
  cursor: default;
}

/* 消息框hover效果取消 */
.ep-message-box__header:hover,
.ep-message-box__title:hover {
  cursor: default;
}

/* 卡片hover效果取消 */
.ep-card:hover {
  box-shadow: var(--ep-card-border);
}

/* 链接hover效果取消 */
.ep-link:hover {
  color: inherit;
}

/* 折叠面板hover效果取消 */
.ep-collapse-item__header:hover {
  background-color: transparent;
}

/* 标签页hover效果取消 */
.ep-tabs__item:hover {
  color: inherit;
}

/* 级联选择器hover效果取消 */
.ep-cascader-node:not(.is-disabled):hover {
  background-color: transparent;
}

/* 时间选择器hover效果取消 */
.ep-time-panel__btn:hover {
  color: inherit;
  background-color: transparent;
}

/* 滑块hover效果取消 */
.ep-slider__button:hover,
.ep-slider__button-wrapper:hover {
  transform: scale(1);
}

/* 上传组件hover效果取消 */
.ep-upload-dragger:hover {
  border-color: var(--ep-upload-border-color);
}

/* 评分组件hover效果取消 */
.ep-rate__item:hover {
  transform: scale(1);
} 
