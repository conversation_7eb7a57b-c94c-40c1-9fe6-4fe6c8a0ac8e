<template>
  <div class="search">
    <!-- 筛选部分 -->
    <el-card class="card-section card-search">
      <template #header>
        <div class="card-header">
          <el-icon><Filter /></el-icon>
          <span>筛选条件</span>
        </div>
      </template>
      <div>
        <el-form
          :inline="true"
          class="search-content form-item-mt-0 search card-search"
        >
          <div v-for="item in searchList" :key="item.label">
            <el-form-item
              :label="item.label"
              :label-width="labelWidth"
              :label-position="'left'"
              style="display: flex"
              class="search-item-label"
            >
              <div style="display: flex">
                <el-checkbox-group
                  v-model="filterOptions[item.key]"
                  style="display: flex"
                >
                  <div style="align-self: flex-start">
                    <el-checkbox-button
                      @change="boxChangeAll(item.key)"
                      class="mr-8"
                      label="全部"
                      value="all"
                    />
                  </div>
                  <div
                    style="
                      display: flex;
                      flex-wrap: wrap;
                      flex: 1;
                      align-self: flex-start;
                    "
                  >
                    <el-checkbox-button
                      @change="boxChangeOther(item.key)"
                      v-for="list in item.list"
                      :key="list.label"
                      :label="list.label"
                      :value="list.value"
                    />
                  </div>
                </el-checkbox-group>
              </div>
            </el-form-item>
          </div>
          <el-button
            type="primary"
            text
            @click="showAllFilter = !showAllFilter"
          >
            {{ showAllFilter ? "收起" : "更多筛选项" }}
            <el-icon class="el-icon--right" v-if="showAllFilter"
              ><CaretTop
            /></el-icon>
            <el-icon class="el-icon--right" v-else><CaretBottom /></el-icon>
          </el-button>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { CaretBottom, CaretTop, Filter } from "@element-plus/icons-vue";
import { computed, ref, toRefs } from "vue";
const labelWidth = "130px";
const showAllFilter = ref(false);
const props = defineProps({
  defaulList: {
    default: [],
    required: true,
  },
  otherList: {
    default: [],
    required: true,
  },
  filterOptions: {
    default: {},
    required: true,
  },
});
const { defaulList, otherList, filterOptions } = toRefs(props);

const emit = defineEmits(["updateFilterOptions"]);

const searchList = computed(() =>
  showAllFilter.value
    ? defaulList.value.concat(otherList.value)
    : defaulList.value
);
const boxChangeOther = (key) => {
  filterOptions.value[key] = filterOptions.value[key].filter((item) => {
    return item !== "all";
  });
  filterOptions.value[key] = filterOptions.value[key].length
    ? filterOptions.value[key]
    : ["all"];
  emit("updateFilterOptions", filterOptions.value);
};
const boxChangeAll = (key) => {
  filterOptions.value[key] = ["all"];
  emit("updateFilterOptions", filterOptions.value);
};
</script>

<style lang="less" scoped>
.mr-8 {
  margin-right: 8px;
}
</style>

<style>
.card-search {
  border-bottom-right-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  .search-content {
    background-color: #f7f8f9;
    padding: 4px;
    text-align: center;
    .el-form-item__label {
      font-size: 12px;
      color: #777;
      padding: 0;
    }

    .el-checkbox-button__inner {
      font-size: 12px;
    }

    .el-checkbox-button.is-checked {
      font-weight: 700;
      color: #328dff;
    }

    .el-checkbox-button--large .el-checkbox-button__inner,
    .el-checkbox-button__inner {
      padding-left: 12px;
      padding-right: 12px;
    }
    .el-form-item {
      margin-bottom: 0px;
    }
  }
  .el-checkbox-button,
  .el-checkbox-button {
    margin: 0 8px 8px 0;
  }

  .el-card__body {
    background-color: #f7f8f9;
  }
}
</style>
