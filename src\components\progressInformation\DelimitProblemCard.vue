<template>
  <div class="card delimit-problem-card" style="margin-top: 24px">
    <div class="title">定界问题</div>
    <div class="description">定界信息</div>
    <el-form
      :model="formData"
      label-width="auto"
      :rules="submitRules"
      ref="formRef"
      :disabled="!canEdit"
    >
      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item
            label="问题分类"
            :label-position="'top'"
            class="form-item"
            prop="classification"
          >
            <el-select
              placeholder="请选择内容"
              v-model="formData.classification"
            >
              <el-option
                v-for="option in classificationOptions"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="故障知识ID"
            :label-position="'top'"
            class="form-item"
            prop="faultKnowledgeID"
            maxlength="100"
          >
            <el-input
              placeholder="请输入内容"
              v-model="formData.faultKnowledgeID"
              @blur="
                formData.faultKnowledgeID = formData.faultKnowledgeID.trim()
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="主责任方"
            :label-position="'top'"
            class="form-item"
            prop="problemBelongings"
          >
            <el-select
              placeholder="请选择内容"
              v-model="formData.problemBelongings"
            >
              <el-option
                v-for="option in problemBelongingsOptions"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            label="初始答复口径"
            :label-position="'top'"
            class="form-item"
          >
            <el-input
              placeholder="请输入weknow知识ID"
              v-model="formData.weknowId"
              @blur="formData.weknowId = formData.weknowId.trim()"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item
        label="定界过程"
        :label-position="'top'"
        class="form-item"
        prop="delimitProgress"
      >
        <template #default>
          <RichText
            v-if="ready"
            v-model="formData.delimitProgress"
            field="delimitProgress"
          ></RichText>
        </template>
      </el-form-item>
      <el-form-item
        label="分析结论"
        :label-position="'top'"
        class="form-item"
        prop="conclusion"
      >
        <template #default>
          <RichText
            v-if="ready"
            v-model="formData.conclusion"
            field="conclusion"
          ></RichText>
        </template>
      </el-form-item>
      <el-form-item
        label="修改建议"
        :label-position="'top'"
        class="form-item"
        prop="modifySuggestion"
      >
        <template #default>
          <RichText
            v-if="ready"
            v-model="formData.modifySuggestion"
            field="modifySuggestion"
          ></RichText>
        </template>
      </el-form-item>
    </el-form>
    <el-table
      class="table"
      :data="localDevices"
      style="width: 100%"
      v-if="formData.problemBelongings !== '非问题'"
    >
      <el-table-column
        :prop="'index'"
        type="index"
        :index="(index) => index + 1"
        :label="'序号'"
        :width="'88px'"
      />
      <el-table-column prop="deviceType">
        <template #header>
          <span><span class="required-field">*</span> 关联单类型</span>
        </template>
        <template #default="scope">
          <el-form-item style="margin-bottom: 0" prop="relatedOrderType">
            <el-select
              v-model="scope.row.relatedOrderType"
              :disabled="!canEdit"
              @change="
                updateOrderStatus($event, scope.row.relatedOrderId, scope.row)
              "
            >
              <el-option
                v-for="item in orderTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="deviceType" :class-name="'order-id'">
        <template #header>
          <span><span class="required-field">*</span> 关联单号</span>
        </template>
        <template #default="scope">
          <template v-if="scope.row.statusEdit">
            <div
              class="id-text"
              @click="
                goRelatedOrder(scope.row, 'relatedOrderId', 'relatedOrderType')
              "
              :class="{ 'common-active-text': scope.row.relatedOrderId }"
            >
              {{ scope.row.relatedOrderId || "---" }}
            </div>
          </template>
          <template v-else>
            <el-form-item style="margin-bottom: 0" prop="relatedOrderId">
              <el-input
                v-model="scope.row.relatedOrderId"
                :disabled="!canEdit"
                @blur="
                  scope.row.relatedOrderId = scope.row.relatedOrderId.trim()
                "
              />
            </el-form-item>
          </template>
          <template v-if="canEdit">
            <template v-if="scope.row.statusEdit">
              <el-icon size="small" @click="updateStatus(scope.row)">
                <EditPen />
              </el-icon>
            </template>
            <template v-else>
              <el-icon size="small" @click="updateStatus(scope.row)">
                <Check />
              </el-icon>
            </template>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="deviceType">
        <template #header>
          <span><span class="required-field">*</span> 关联单状态</span>
        </template>
        <template #default="scope">
          <div>{{ scope.row.orderStatus || "---" }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!flowInfo.isAllFinished && canEdit"
        fixed="right"
        label="操作"
        width="120"
      >
        <template #default="scope">
          <el-icon
            :disabled="localDevices.length <= 1"
            @click="handleDelete(scope.$index)"
          >
            <Delete />
          </el-icon>
          <el-icon
            v-if="scope.$index === localDevices.length - 1"
            link
            type="primary"
            size="small"
            @click="addRow"
            style="margin-left: 16px; color: rgb(10, 89, 247)"
          >
            <Plus />
          </el-icon>
        </template>
      </el-table-column>
    </el-table>
    <Upload
      ref="uploadRef"
      :uploadFileParams="uploadFileParams"
      :canEdit="canEdit"
      style="margin-top: 20px"
    ></Upload>

    <div class="description" v-if="!isNodeFinished">操作信息</div>
    <el-form
      :model="formData"
      label-width="auto"
      label-position="top"
      :rules="submitRules"
      :disabled="!canEdit"
      v-if="!isNodeFinished"
    >
      <div style="margin-top: 24px; display: flex; align-items: center">
        <el-form-item
          label="操作类型"
          prop="operationType"
          style="margin-right: 20px"
        >
          <el-radio-group
            v-model="formData.operationType"
            @change="changeOperationType"
          >
            <el-radio :value="operationOpts[1].value">{{
              operationOpts[1].label
            }}</el-radio>
            <el-radio :value="operationOpts[0].value">{{
              operationOpts[0].label
            }}</el-radio>
            <el-radio :value="operationOpts[2].value">{{
              operationOpts[2].label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="formData.operationType !== '0002'"
          label="指定处理人"
          prop="nextHandler"
          style="margin-left: 100px; width: 20%"
        >
          <el-select v-model="formData.nextHandler" filterable>
            <el-option
              v-for="item in handlerOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <el-form-item
        :label="formData.operationType === '0000' ? '转单原因' : '驳回原因'"
        :label-position="'top'"
        v-if="
          formData.operationType === '0000' || formData.operationType === '0002'
        "
        class="form-item"
        prop="reason"
      >
        <el-input
          type="textarea"
          :placeholder="
            formData.operationType === '0000'
              ? '请输入转单原因'
              : '请输入驳回原因'
          "
          v-model="formData.reason"
          show-word-limit
          maxlength="2000"
          @blur="formData.reason = formData.reason.trim()"
        />
      </el-form-item>
    </el-form>

    <div v-if="canEdit" class="text-center">
      <el-button
        v-if="isNodeFinished"
        id="save-button"
        @click="showSubmitDialog(true)"
        >{{ "更新信息" }}</el-button
      >
      <el-button v-if="!isNodeFinished" id="save-button" @click="onSave">{{
        "保存"
      }}</el-button>
      <el-button
        v-if="!isNodeFinished"
        type="primary"
        id="submit-button"
        @click="showSubmitDialog(false)"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, toRefs } from "vue";
import { ElMessage, ElLoading, ElMessageBox } from "element-plus";
import { orderTypeOptions } from "@/business/progressInformation/relateOrder";
import { Delete, Plus, EditPen, Check } from "@element-plus/icons-vue";
import {
  problemBelongingsOptions,
  saveDelimitProblem,
  operationTypeOptions as operationOpts,
  submitDelimitProblem,
  fetchDeLmitProblemData,
} from "@/business/progressInformation/delimitProblem";
import { useRoute } from "vue-router";
import { getHandlers } from "@/business/progressInformation/common/getHandlers";
import { weKnowIDReg } from "@/business/progressInformation/common/regex";
import { classificationOptions } from "@/components/issuesOpinionManage/commonArea/checkList";
import { useFlowInfoStore } from "@/store";
import { cloneDeep, isEqual } from "lodash-es";
import { validateFaultKnowledgeID } from "@/utils";
import {
  getValidateFun,
  validateTable,
} from "@/business/progressInformation/common/validateUtil";
import Upload from "./common/Upload.vue";
import {
  goRelatedOrder,
  updateOrderStatus,
} from "@/utils/opinionManagementDetail";
import { validateOrderUtil } from "@/utils/opinionManagementDetail";
import RichText from "./common/RichText.vue";

const { flowInfo } = useFlowInfoStore();
const emit = defineEmits(["refresh"]);
const refreshParentData = (step = null) => {
  emit("refresh", step);
};
const uploadRef = ref();

const ready = ref(false);

const uploadFileParams = ref([]);
// 存储本地编辑的数据，最终提交时才会发送给后端
const localDevices = ref([]);
const props = defineProps({
  node: {
    default: () => {},
    required: true,
  },
  canEdit: {
    default: () => false,
    required: true,
  },
});

const formRef = ref();

const handlerOptions = ref([]);
const changeOperationType = async (operationType) => {
  formData.value.reason = "";
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  let handlerList = null;
  if (opinionIssueId) {
    handlerList = await getHandlers(opinionIssueId, operationType);
  }
  if (handlerList) {
    handlerOptions.value = handlerList.handlers;
    const handlerListCount = handlerList.handlers.length;
    const randomIndex = Math.floor(Math.random() * handlerListCount);
    if (
      handlerList.defaultHandler ||
      operationType === operationOpts[0].value
    ) {
      // 有默认处理人或转单时使用后台默认处理人
      formData.value.nextHandler = handlerList.defaultHandler;
    } else {
      // 提交时若后台默认处理人为空，则默认随机选中一个下一步处理人
      formData.value.nextHandler = handlerList.handlers[randomIndex].value;
    }
  } else {
    ElMessage({
      type: "error",
      message: "获取下一步处理人失败",
    });
  }
  loading.close();
};

const submitRules = {
  classification: [
    { required: true, message: "请输入问题分类", trigger: "blur" },
    { validator: getValidateFun("问题分类"), trigger: "blur" },
  ],
  problemBelongings: [
    { required: true, message: "请输入主责任方", trigger: "blur" },
    { validator: getValidateFun("主责任方"), trigger: "blur" },
  ],
  delimitProgress: [
    { required: true, message: "请输入定界过程", trigger: "blur" },
    { validator: getValidateFun("定界过程"), trigger: "blur" },
  ],
  conclusion: [
    { required: true, message: "请输入分析结论", trigger: "blur" },
    { validator: getValidateFun("分析结论"), trigger: "blur" },
  ],
  modifySuggestion: [
    { required: true, message: "请输入修改建议", trigger: "blur" },
    { validator: getValidateFun("修改建议"), trigger: "blur" },
  ],
  operationType: [
    { required: true, message: "请输入操作类型", trigger: "blur" },
  ],
  nextHandler: [
    { required: true, message: "请输入下一步处理人", trigger: "blur" },
  ],
  reason: [{ required: true, message: "请输入原因", trigger: "blur" }],
};

const { node } = toRefs(props);
const isNodeFinished = node.value.isFinished;

const route = useRoute();
let formData = ref({
  classification: "",
  faultKnowledgeID: "",
  problemBelongings: "",
  conclusion: "",
  modifySuggestion: "",
  delimitProgress: "",
  operationType: "",
  reason: "",
  weknowId: "",
  nextHandler: "",
  relateWorkOrders: [],
});

const originData = ref({});

// 初始化时获取数据
onMounted(async () => {
  if (opinionIssueId) {
    const concertedData = await fetchDeLmitProblemData(opinionIssueId);
    formData.value = concertedData;
    uploadFileParams.value = concertedData.attachmentInfos;
    concertedData.relateWorkOrders.forEach((item) => {
      item.statusEdit = true;
    });
    // 初始化本地设备数据
    localDevices.value = concertedData.relateWorkOrders;
    originData.value = cloneDeep({
      classification: formData.value.classification,
      faultKnowledgeID: formData.value.faultKnowledgeID,
      problemBelongings: formData.value.problemBelongings,
      conclusion: formData.value.conclusion,
      modifySuggestion: formData.value.modifySuggestion,
      delimitProgress: formData.value.delimitProgress,
      operationType: formData.value.operationType,
      weknowId: formData.value.weknowId,
      localDevices: localDevices.value,
      uploadFile:
        concertedData.attachmentInfos?.map((item) => {
          return {
            id: item.id,
            filePath: item.filePath,
          };
        }) || [],
    });
    // 如果没有数据，添加一条默认数据
    if (localDevices.value.length === 0) {
      addRow();
    }
    ready.value = true;
  }
});

// 添加新行
const addRow = () => {
  const newRow = {
    relatedOrderType: "",
    relatedOrderId: "",
    statusEdit: true,
    orderStatus: "",
  };
  localDevices.value.push(newRow);
};
const opinionIssueId = route.query.id;

// 更改本行状态
const updateStatus = (data: any) => {
  if (!data.statusEdit) {
    data.orderStatus = "";
    if (validateOrderUtil({ relatedOrderId: data.relatedOrderId, relatedOrderType: data.relatedOrderType })) {
      data.statusEdit = !data.statusEdit;
      updateOrderStatus(data.relatedOrderType, data.relatedOrderId, data);
    }
  } else {
    data.statusEdit = !data.statusEdit;
  }
};

// 保存
const onSave = async () => {
  if (formData.value.weknowId) {
    if (!weKnowIDReg.test(formData.value.weknowId)) {
      ElMessage({
        type: "error",
        message: "请输入正确的知识id",
      });
      return false;
    }
  }
  if (formData.value.faultKnowledgeID) {
    if (!validateFaultKnowledgeID(formData.value.faultKnowledgeID)) {
      ElMessage({
        type: "error",
        message: "请输入正确的故障知识ID",
      });
      return false;
    }
  }
  if (hasDuplicateRelatedOrderId()) {
    ElMessage({
      type: "error",
      message: "关联单号不能重复",
    });
    return false;
  }
  try {
    formData.value.relateWorkOrders = localDevices.value;
    formData.value.opinionIssueId = opinionIssueId;
    await saveDelimitProblem({
      ...formData.value,
      attachmentItems: uploadRef.value.getFileListParams(),
    });
    // 显示成功提示
    ElMessage({
      type: "success",
      message: "保存成功",
    });
    refreshParentData(node.value.step);
  } catch (err) {
    console.error("保存失败:");
    ElMessage({
      type: "error",
      message: "保存失败",
    });
  }
};
// 校验操作信息
const checkOperation = () => {
  if (!formData.value.nextHandler && !formData.value.operationType) {
    return false;
  }
  return true;
};

// 校验 relatedOrderId 是否重复
const hasDuplicateRelatedOrderId = () => {
  const seen = new Set();
  return localDevices.value.some((item) => {
    const id = item.relatedOrderId;
    if (!id) return false;
    if (seen.has(id)) return true;
    seen.add(id);
    return false;
  });
};

// 提交
const showSubmitDialog = async (update = false) => {
  if (formData.value.weknowId) {
    if (!weKnowIDReg.test(formData.value.weknowId)) {
      ElMessage({
        type: "error",
        message: "请输入正确的知识id",
      });
      return false;
    }
  }
  if (
    (formData.value.operationType === "0000" ||
      formData.value.operationType === "0002") &&
    !formData.value.reason
  ) {
    ElMessage({
      type: "error",
      message: "请填写原因",
    });
    return false;
  }
  if (formData.value.faultKnowledgeID) {
    if (!validateFaultKnowledgeID(formData.value.faultKnowledgeID)) {
      ElMessage({
        type: "error",
        message: "请输入正确的故障知识ID",
      });
      return false;
    }
  }
  if (
    isNodeFinished &&
    isEqual(
      {
        classification: formData.value.classification,
        faultKnowledgeID: formData.value.faultKnowledgeID,
        problemBelongings: formData.value.problemBelongings,
        conclusion: formData.value.conclusion,
        modifySuggestion: formData.value.modifySuggestion,
        delimitProgress: formData.value.delimitProgress,
        operationType: formData.value.operationType,
        weknowId: formData.value.weknowId,
        localDevices: localDevices.value,
        uploadFile: uploadRef.value.getFileListParams().map((item) => {
          return { id: item.id, filePath: item.filePath };
        }),
      },
      originData.value
    )
  ) {
    ElMessage({
      type: "error",
      message: "内容未修改,请修改内容后更新",
    });
    return false;
  }
  if (
    formData.value.operationType === "0000" ||
    formData.value.operationType === "0002"
  ) {
    onSubmit(update);
    return;
  }
  if (hasDuplicateRelatedOrderId()) {
    ElMessage({
      type: "error",
      message: "关联单号不能重复",
    });
    return false;
  }
  if (formData.value.problemBelongings !== "非问题") {
    const hasEmptyFields = localDevices.value.some(
      (item) =>
        !item.relatedOrderType ||
        !item.relatedOrderId ||
        !validateTable(item.relatedOrderType) ||
        !validateTable(item.relatedOrderId)
    );
    if (hasEmptyFields) {
      ElMessage({
        type: "error",
        message: "请完成关联单类型和关联单号的填写",
      });
      return;
    }
  }
  if (formData.value.problemBelongings !== "非问题") {
    let isValidate = true;
    localDevices.value.forEach((item) => {
      if (!validateOrderUtil(item)) {
        isValidate = false;
      }
    });
    if (!isValidate) return;
  }
  if (!isNodeFinished && !checkOperation()) {
    ElMessage({
      type: "error",
      message: "请填写操作类型和处理人",
    });
    return false;
  }
  await formRef.value.validate((valid) => {
    if (valid) {
      onSubmit(update);
    }
  });
};
const onSubmit = async (update = false) => {
  if (formData.value.problemBelongings === "非问题") {
    formData.value.relateWorkOrders = [];
  } else {
    formData.value.relateWorkOrders = localDevices.value;
  }
  formData.value.opinionIssueId = opinionIssueId;

  try {
    await submitDelimitProblem({
      ...formData.value,
      attachmentItems: uploadRef.value.getFileListParams(),
    });
    // 显示成功提示
    ElMessage({
      type: "success",
      message: "提交成功",
    });
    refreshParentData(update ? node.value.step : null);
  } catch (err) {
    console.error("提交失败:", err);
    ElMessage({
      type: "error",
      message: "提交失败",
    });
  }
};

// 删除行
const handleDelete = (index: number) => {
  // 如果只有一条数据，不允许删除
  if (localDevices.value.length <= 1) {
    ElMessage({
      type: "warning",
      message: "至少需要保留一条复现条件",
    });
    return;
  }
  // 确认删除
  ElMessageBox.confirm("确认删除该问题单?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // 直接从本地数组中移除，不需要跟踪已删除的ID
    localDevices.value.splice(index, 1);

    ElMessage({
      type: "success",
      message: "删除成功",
    });
  });
};
</script>

<style lang="scss" scoped>
@import "@/styles/common-styles.scss";
.delimit-problem-card {
  .el-form {
    margin: 16px 0;
  }

  #save-button,
  #submit-button {
    margin-top: 24px;
    width: 96px;
  }
}

/* 添加必填项红色星号样式 */
.required-field {
  color: #f56c6c;
  margin-left: 4px;
}

:deep(.el-form-item__error) {
  position: absolute;
  top: 100%;
  left: 0;
  padding-top: 2px;
  font-size: 12px;
  color: #f56c6c;
}

.el-icon {
  cursor: pointer;

  &.disabled {
    cursor: not-allowed;
    color: gray;
  }
}

:deep(td.order-id) {
  .cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .el-form-item {
      width: calc(100% - 30px);
    }
    .id-text {
      max-width: calc(100% - 30px);
    }
  }
}
</style>
