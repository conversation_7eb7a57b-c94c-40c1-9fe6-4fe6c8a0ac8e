import { ewpService } from "@/utils/axios"

export const getOriginChartData = async (data) => {
  const response = await ewpService.post(`/statisitc/getOpinionDashboard`, data)
  return response
}

export const queryTotalVolume = async (data) => {
  const response = await ewpService.post(`/statisitc/queryTotalVolume`, data)
  return response
}
export const queryCompletionRate = async (data) => {
  const response = await ewpService.post(`/statisitc/queryCompletionRate`, data)
  return response
}
export const queryProblemCount = async (data) => {
  const response = await ewpService.post(`/statisitc/queryProblemCount`, data)
  return response
}

export const getSolutionChartData = async (data) => {
  try {
    const response = await ewpService.post(`/statisitc/queryPlanSolveData`, data)
    return response
  } catch (e) {
    throw e
  }
}

export const getSLAData = async (data) => {
  try {
    const response = await ewpService.post(`/statisitc/queryOpinionIssueSLAData`, data)
    return response
  } catch (e) {
    throw e
  }
}

export const getIssueCountData = async (data) => {
  try {
    const response = await ewpService.post(`/statisitc/queryOpinionIssueCountData`, data)
    return response
  } catch (e) {
    throw e
  }
}

export const getVolumeData = async (data) => {
  try {
    const response = await ewpService.post(`/statisitc/queryOriginCountData`, data)
    return response
  } catch (e) {
    throw e
  }
}