// 面包屑
.el-breadcrumb {
  --el-text-color-primary: #a9abad;

  .el-breadcrumb__inner.is-link,
  .el-breadcrumb__separator {
    font-weight: normal;
  }
}

// Tabs
.el-tabs__item {
  color: #777777;
}

.el-tabs__item.is-active {
  color: #000;
  font-size: 20px;
  font-weight: bold;
}

.el-tabs__item:hover {
  color: #000;
}

.el-tabs__header {
  margin: 0 0 25px;
}

// 锚点
.el-anchor {
  --el-bg-color: var(--transparent-color);

  .el-anchor__link {
    font-size: 14px;
    text-align: start;
    padding: 5px 0;
  }
}

.el-anchor.el-anchor--vertical .el-anchor__marker {
  height: 32px;
}

.el-splitter-bar__dragger:before,
.el-splitter-bar__dragger:hover:before {
  background-color: var(--transparent-color) !important;
}

// dialog
.el-dialog {
  padding: 24px;

  .el-dialog__header {
    text-align: start;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
  }

  .el-dialog__headerbtn {
    width: 64px;
    height: 64px;
    --el-color-info: var(--default-color);
  }
}
