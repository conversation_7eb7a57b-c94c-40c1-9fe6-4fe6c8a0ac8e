<template>
  <div class="comp-chart">
    <div class="title">{{ title }}</div>
    <div style="display: flex">
      <el-select
        v-if="sourceData.select"
        v-model="scourceValues"
        :ref="sourceData.select.name"
        multiple
        clearable
        collapse-tags
        placeholder="请选择问题来源"
        popper-class="custom-header"
        :max-collapse-tags="1"
        style="width: 160px"
        @change="(val) => handleSourceChange(sourceData.select.name, val)"
      >
        <template #header>
          <el-checkbox
            v-model="checkAll"
            :indeterminate="indeterminate"
            @change="(val) => handleCheckAll(sourceData.select.name, val)"
          >
            全部
          </el-checkbox>
        </template>
        <el-option
          v-for="item in sourceData.select.selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="canvas-wrap" :id="sourceData.id"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, toRefs } from "vue";
import * as echarts from "echarts";
import type { CheckboxValueType } from "element-plus";
const scourceValues = ref<CheckboxValueType[]>([]);
const checkAll = ref(false);
const indeterminate = ref(false);

const props = defineProps({
  sourceData: {
    default: () => {},
    required: true,
  },
  useKey: {
    default: () => "",
    required: true,
  },
  title: {
    default: () => "",
    required: true,
  },
});

const emit = defineEmits(["updateSelectVal"]);

const { sourceData } = toRefs(props);
const { useKey } = toRefs(props);

const allSourceOpts = ref(sourceData.value.select?.selectOptions || []);

watch(scourceValues, (val) => {
  if (val.length === 0) {
    checkAll.value = false;
    indeterminate.value = false;
  } else if (val.length === allSourceOpts.value.length) {
    checkAll.value = true;
    indeterminate.value = false;
  } else {
    indeterminate.value = true;
  }
});
watch(useKey, (old, val) => {
  if (old !== val) {
    reloadAllSourceChart();
  }
});
const handleCheckAll = (selectName, val: CheckboxValueType) => {
  indeterminate.value = false;
  if (val) {
    scourceValues.value = allSourceOpts.value.map((_) => _.value);
  } else {
    scourceValues.value = [];
  }
  emit("updateSelectVal", { name: selectName, value: scourceValues.value });
};

const handleSourceChange = (selectName, value) => {
  emit("updateSelectVal", { name: selectName, value });
};

onMounted(() => {
  reloadAllSourceChart();
});

const chartInit = (id, option) => {
  const canvasEle = document.getElementById(id);
  const myChart = echarts.init(canvasEle, null, {
    width: canvasEle?.offsetWidth,
    height: canvasEle?.offsetHeight,
  });
  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(option);
};

const allSourceMonitorModule = () => {
  const chartId = sourceData.value.id;
  // 指定图表的配置项和数据
  const reloadAllSourceChart = () => chartInit(chartId, sourceData.value);
  return {
    reloadAllSourceChart,
  };
};

const { reloadAllSourceChart } = allSourceMonitorModule();
</script>

<style lang="less" scoped>
.comp-chart {
  display: flex;
  flex-direction: column;
  height: 100%;
  .title {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
  }
  .canvas-wrap {
    flex: 1;
    align-self: stretch;
    height: 100%;
  }
}
</style>
