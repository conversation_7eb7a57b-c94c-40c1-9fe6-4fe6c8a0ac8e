// 筛选条件样式
.search {
  /* 单选 */
  .el-radio {
    margin-right: 32px;
  }
  .el-radio-button {
    border-radius: 4px;
    overflow: hidden;
    --el-radio-input-border: 1px solid rgb(194, 194, 194);
  }
  .el-radio-button.is-active {
    --el-radio-button-checked-bg-color: rgba(10, 89, 247, 0.1);
    --el-radio-button-checked-text-color: var(--primary-color);
    --el-radio-button-checked-border-color: var(--transparent-color);
    border: unset;
  }
  .el-radio-button:first-child .el-radio-button__inner {
    border-left: unset;
  }
  .el-radio-button--large .el-radio-button__inner,
  .el-radio-button__inner {
    border: unset;
    padding: 9px 12px;
  }
  // 圆形单选
  .el-radio__inner {
    --el-radio-input-bg-color: var(--transparent-color);
  }
  .el-radio-group,
  .el-checkbox-group {
    --el-border-radius-base: 4px;
    margin-bottom: 0;
    text-align: start;
  }
  /* 多选 */
  .el-checkbox-button {
    border-radius: 4px;
    overflow: hidden;
    --el-checkbox-button-checked-bg-color: rgba(10, 89, 247, 0.1);
    --el-checkbox-button-checked-text-color: var(--primary-color);
    --el-checkbox-button-checked-border-color: var(--transparent-color);
    border: unset;
    --el-border: 1px solid var(--transparent-color);
  }
  .el-checkbox-button,
  .el-radio-button {
    margin-left: 12px;
    margin-bottom: 16px;
  }
  .el-checkbox-button__inner {
    padding: 9px 12px;
  }
}
/* 输入框、下拉框、级联去掉focus样式 */
.el-cascader .el-input.is-focus .el-input__wrapper,
.el-input__wrapper.is-focus,
.el-select__wrapper.is-focused,
.el-textarea__inner:focus {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

// 多行输入
.el-textarea {
  .el-textarea__inner {
    padding: 8px 12px 10px 12px;
  }
}

/* 必填星号 */
.el-form-item.is-required:not(.is-no-asterisk).asterisk-left
  > .el-form-item__label-wrap
  > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk).asterisk-left
  > .el-form-item__label:before {
  color: #f43146;
}

// 左对齐必填与不必填对齐方式，需要与文本对齐，加上unRequired类
.el-form-item.unRequired {
  .el-form-item__label {
    padding-left: 10.375px;
  }
}

// 如果需要去掉form表单里的下间距，加 form-item-mt-0 类
.form-item-mt-0 .el-form-item {
  margin-bottom: 0;
}

/* 筛选样式 */
/* 头部 */
.search-top {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.9);
  height: 40px;
  background-color: var(--screening-heavy-bg-color);
  padding-left: 24px;
  border-radius: 12px 12px 0px 0px;
  .el-icon {
    color: rgba(0, 0, 0, 0.45);
  }
}
/* 表单容器 */
.search-content {
  padding: 24px;
  background-color: var(--screening-light-bg-color);
}

/* 拼接的表单 */
/* 前面的 */
/* select */
.pre {
  /* 可根据自己的文本多少添加新的类设置 */
  width: 104px;
}
.pre .el-select__wrapper {
  box-shadow: none;
  border: 1px solid var(--el-border-color);
  border-right: unset;
}
.pre .el-select__wrapper.is-hovering {
  box-shadow: none;
}
.pre .el-icon {
  color: var(--default-color);
}
.pre .el-select__wrapper {
  border-radius: 8px 0px 0px 8px;
}
/* button */
.preButton {
  padding: 0px 12px 0px 12px;
  height: 32px;
  line-height: 30px;
  color: #b1b3b9;
  border: 1px solid var(--el-border-color);
  border-right: none;
  border-radius: var(--el-border-radius-base) 0 0 var(--el-border-radius-base);
  box-sizing: border-box;
}
/* 后面的 */
.next {
  width: 229px;
}
/* input */
.next .el-input__wrapper {
  border-radius: 0px 8px 8px 0px;
}
/* 下拉选择 */
.next .el-select__wrapper {
  border-radius: 0px 8px 8px 0px;
}
.next.el-date-editor--daterange {
  border-radius: 0px 8px 8px 0px;
}

.select-left .el-select__wrapper {
  border-radius: 8px 0px 0px 8px ;
}
