import { getValidateFun } from "@/business/progressInformation/common/validateUtil";

export const submitRules = {
  level: [{ required: true, message: "问题等级必填！", trigger: "blur" }],
  degree: [{ required: true, message: "严重程度必填！", trigger: "blur" }],
  classification: [
    { required: true, message: "问题分类必填！", trigger: "blur" },
  ],
  description: [{ required: true, message: "问题描述必填！", trigger: "blur" }],
};

export const rulesAssignment = {
  operationType: [
    { required: true, message: "操作类型必填！", trigger: "blur" },
  ],
  handler: [{ required: true, message: "指定处理人必填！", trigger: "blur" }],
  nextHandler: [
    { required: true, message: "指定处理人必填！", trigger: "blur" },
  ],
  reason: [{ required: true, message: "原因必填！", trigger: "blur" }]
};

export const levelOptions = [
  {
    label: "A",
    value: "A",
  },
  {
    label: "B",
    value: "B",
  },
  {
    label: "C",
    value: "C",
  },
  {
    label: "D",
    value: "D",
  },
  {
    label: "其他",
    value: "其他",
  },
];

export const degreeOptions = [
  {
    label: "严重",
    value: "严重",
  },
  {
    label: "致命",
    value: "致命",
  },
  {
    label: "一般",
    value: "一般",
  },
  {
    label: "提示",
    value: "提示",
  },
];

export const recurrentTableData = [
  {
    deviceType: "---",
    deviceModel: "---",
    probability: "---",
    reproductionPath: "---",
    appVersion: "---",
    osVersion: "---",
  },
];

export const recurrentTableColumns = [
  {
    label: "产品类型",
    key: "deviceType",
  },
  {
    label: "产品机型",
    key: "deviceModel",
  },
  {
    label: "复现概率",
    key: "probability",
  },
  {
    label: "复现路径",
    key: "reproductionPath",
  },
  {
    label: "应用版本",
    key: "appVersion",
  },
  {
    label: "OS版本",
    key: "osVersion",
  },
];

export const description = "微信使用期间输入文字没有关联表情，很不方便";
