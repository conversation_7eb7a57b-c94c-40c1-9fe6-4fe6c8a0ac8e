<template>
  <div class="page-statistical">
    <div class="page-title-section">
      <div class="page-title">问题统计看板</div>
      <div class="tools-bar">
        <el-select
          v-model="appTypeListValues"
          multiple
          clearable
          collapse-tags
          :max-collapse-tags="1"
          placeholder="请选择应用层级"
          popper-class="custom-header"
          class="select-app-type"
        >
          <template #header>
            <el-checkbox
              v-model="checkAll"
              :indeterminate="indeterminate"
              @change="handleCheckAll"
            >
              全选
            </el-checkbox>
          </template>
          <el-option
            v-for="item in appLayerList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>

        <el-input
          class="input-app-name"
          v-model="filterOptions.appName"
          style="max-width: 600px"
          placeholder="请输入应用"
          clearable
        >
          <template #append>
            <el-button :icon="Search" @click="invokeChartData" />
          </template>
        </el-input>
        <el-date-picker
          class="date-range"
          v-model="filterOptions.timeSegment"
          :type="dateType"
          range-separator="到"
          start-placeholder="开始时间"
          :disabledDate="disabledDate"
          end-placeholder="结束时间"
          clearable
          @calendar-change="calendarChange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="changeDate"
          unlink-panels
        />
        <el-radio-group
          class="radio-dateUnit"
          v-model="filterOptions.type"
          fill="rgba(255,255,255)"
          text-color="#333"
          @change="invokeChartData"
        >
          <el-radio-button label="日" value="day" />
          <el-radio-button label="周" value="week" />
          <el-radio-button label="月" value="month" />
        </el-radio-group>
      </div>
    </div>

    <el-card class="card-section">
      <template #header>
        <div class="card-header">
          <el-icon><PieChart /></el-icon>
          <span>问题解决进展分析</span>
        </div>
      </template>
      <div class="chart-body">
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="issueCountChartData.show"
            :sourceData="issueCountChartData"
            :useKey="issueCountChartData.key"
            :title="issueCountChartData.chartTitle"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="questionDistributionData.show"
            :sourceData="questionDistributionData"
            :useKey="questionDistributionData.key"
            :title="questionDistributionData.chartTitle"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="planOnlineTimeDistributionData.show"
            :sourceData="planOnlineTimeDistributionData"
            :useKey="planOnlineTimeDistributionData.key"
            :title="planOnlineTimeDistributionData.chartTitle"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="volumeChartData.show"
            :sourceData="volumeChartData"
            :useKey="volumeChartData.key"
            :title="volumeChartData.chartTitle"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="slaAnalysis.show"
            :sourceData="slaAnalysis"
            :useKey="slaAnalysis.key"
            :title="slaAnalysis.chartTitle"
          ></sourcesVolumeChart>
        </div>
        <div class="chart-section">
          <sourcesVolumeChart
            v-if="solutionCountAndRate.show"
            :sourceData="solutionCountAndRate"
            :useKey="solutionCountAndRate.key"
            :title="solutionCountAndRate.chartTitle"
          ></sourcesVolumeChart>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import type { CheckboxValueType } from "element-plus";
import { Search, PieChart } from "@element-plus/icons-vue";
import {
  appLayerList,
  problemSource,
} from "../../components/issuesOpinionManage/commonArea/checkList";
import sourcesVolumeChart from "./sourcesVolumeChart.vue";
import {
  getOriginChartData,
  queryTotalVolume,
  queryCompletionRate,
  queryProblemCount,
  getSolutionChartData,
  getSLAData,
  getIssueCountData,
  getVolumeData,
} from "./chartApi";
import { statusMap } from "./index";

const chooseDate = ref("");
const disabledDate = (time: Date) => {
  if (chooseDate.value) {
    if (filterOptions.value.type === "day") {
      return time.getTime() == chooseDate.value || time > new Date();
    } else {
      // 1天的时间戳
      const timeRange = 1 * 24 * 60 * 60 * 1000;
      const rangeMap = {
        week: 6 * timeRange,
        month: 30 * timeRange,
      };
      const minTime = chooseDate.value - rangeMap[filterOptions.value.type];
      const maxTime = chooseDate.value + rangeMap[filterOptions.value.type];
      return (
        !(time.getTime() <= minTime || time.getTime() > maxTime) ||
        time > new Date()
      );
    }
  } else {
    return time > new Date();
  }
};
const calendarChange = (val: any) => {
  chooseDate.value = val[0].getTime(); // 点击第一次选中日期
  if (val[1]) {
    chooseDate.value = "";
  } // 选中后必须清空
};

const getAreaStyle = (color1, color2) => {
  return {
    opacity: 0.8,
    color: {
      type: "linear",
      x: 1,
      y: -3,
      x2: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: color1, // 0% 处的颜色
        },
        {
          offset: 1,
          color: color2, // 100% 处的颜色
        },
      ],
      global: false, // 缺省为 false}
    },
  };
};

// 问题类型分布
let questionDistributionData = ref({});

// 问题闭环处理趋势
let problemClosureData = ref({});

// 计划上线时间分布
const planOnlineTimeDistributionData = ref({});

const checkAll = ref(false);
const indeterminate = ref(false);
const appTypeListValues = ref<CheckboxValueType[]>([]);
watch(appTypeListValues, (val) => {
  if (val.length === 0) {
    checkAll.value = false;
    indeterminate.value = false;
  } else if (val.length === appLayerList.length) {
    checkAll.value = true;
    indeterminate.value = false;
  } else {
    indeterminate.value = true;
  }
  filterOptions.value.appPriority = appTypeListValues;
  getChartData();
  fetchSolutionChart();
  fetchSLAChart();
  fetchIssueCount();
  fetchVolumeChart();
});

const handleCheckAll = (val: CheckboxValueType) => {
  indeterminate.value = false;
  if (val) {
    appTypeListValues.value = appLayerList.map((_) => _.value);
  } else {
    appTypeListValues.value = [];
  }
};

const filterOptions = ref({
  appPriority: [],
  appName: "",
  timeSegment: [],
  type: "day",
  sourceSelect: [],
});

const setProblemClosureData = (xAxisData, series) => {
  problemClosureData.value = {
    key: new Date().getTime(),
    show: false,
    chartTitle: "问题闭环处理趋势",
    id: "problemClosure",
    legend: {
      bottom: 0,
      left: "center",
      icon: "circle",
      itemWdith: 12,
      itemHeight: 12,
    },
    title: {
      text: "",
    },
    tooltip: {
      textStyle: {
        // fontSize: 11,
      },
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    xAxis: {
      type: "category",
      data: xAxisData,
    },
    yAxis: {},
    series: [
      {
        name: "问题识别",
        tag: "0000",
        stack: "Total",
        smooth: true,
        type: "line",
        color: "rgb(32, 112, 243)",
        areaStyle: {
          ...getAreaStyle("blue", "rgba(32, 112, 243, 0.1)"),
        },
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["问题识别"]],
        // data: [13887, 15620, 26820, 40779, 50142, 54709, 55284, 55930],
      },
      {
        name: "问题定界",
        tag: "0001",
        stack: "Total",
        smooth: true,
        type: "line",
        color: "rgba(98, 180, 46, 1)",
        areaStyle: {
          ...getAreaStyle("rgba(98, 180, 46, 1)", "rgba(98, 180, 46, 0.1)"),
        },
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["问题定界"]],
      },
      {
        name: "计划锁定",
        tag: "0002",
        stack: "Total",
        smooth: true,
        type: "line",
        color: "#6D5BF2",
        areaStyle: {
          ...getAreaStyle("#6D5BF2", "rgba(109, 91, 242, 0.1)"),
        },
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["计划锁定"]],
      },
      {
        name: "问题修复",
        smooth: true,
        tag: "0003",
        stack: "Total",
        type: "line",
        areaStyle: {
          ...getAreaStyle("rgba(44, 184, 201, 1)", "rgba(44, 184, 201, 0.1)"),
        },
        color: "rgb(44, 184, 201)",
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["问题修复"]],
      },
      {
        name: "验证闭环",
        type: "line",
        stack: "Total",
        tag: "0004",
        smooth: true,
        areaStyle: {
          ...getAreaStyle("rgba(246, 158, 57, 1)", "rgba(246, 158, 57, 0.1)"),
        },
        color: "rgba(246, 158, 57, 1)",
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["验证闭环"]],
      },
      {
        name: "关闭",
        type: "line",
        stack: "Total",
        tag: "0005",
        smooth: true,
        color: "rgba(232, 64, 38, 1)",
        areaStyle: {
          ...getAreaStyle("#FCE3DF", "rgba(232, 64, 38, 0.1)"),
        },
        label: {
          show: false,
        },
        showSymbol: false,
        data: series[statusMap["关闭"]],
      },
    ],
  };
  problemClosureData.value.show = true;
};
const setQuestionDistributionData = (faultCategoryRatio) => {
  questionDistributionData.value = {
    show: true,
    key: new Date().getTime(),
    chartTitle: "问题类型分布",
    id: "questionDistribution",
    title: {
      text: "",
    },
    tooltip: {
      textStyle: {},
      trigger: "item",
      formatter: (params) => {
        // params 是一个数组，包含所有被触发 action 的数据点信息
        let res =
          params.marker +
          "<span style='display:inline-block;width:5px;'></span>";
        res +=
          params.name +
          "<span style='display:inline-block;width:10px;'></span>";
        res += params.value + "%"; // 假设这里的单位是固定的，例如“单位”
        res += "<br/>";
        return res;
      },
    },
    legend: {
      bottom: 0,
      left: "center",
      icon: "circle",
      itemWdith: 12,
      itemHeight: 12,
    },
    series: [
      {
        name: "",
        type: "pie",
        radius: ["40%", "55%"],
        center: ["50%", "40%"],
        label: {
          formatter: (params) => {
            let res = `${params.data.name}\n${params.data.value}%`;
            return res;
          },
          align: "center",
          lineHeight: 17,
        },
        data: faultCategoryRatio,
      },
    ],
  };
};

// 时间选择器的单位与当前筛选的时间单位日周月保持一致
const dateType = computed(() => {
  return filterOptions.value.type === "month" ? "monthrange" : "daterange";
});
const checkDate = () => {
  let checked = false;
  const val = filterOptions.value.timeSegment;
  if (val && filterOptions.value.type === "week") {
    const timeRange = 1 * 24 * 60 * 60 * 1000;
    checked =
      new Date(val[1]).getTime() < 6 * timeRange + new Date(val[0]).getTime();
  } else if (val) {
    const mounthStart = new Date(val[0]).getMonth();
    const mounthEnd = new Date(val[1]).getMonth();
    // 类型筛选为月份的时候开始日期月份与结束日期月份相同或者开始日期等于结束日期校验不通过
    checked =
      (filterOptions.value.type === "month" && mounthStart == mounthEnd) ||
      val[0] == val[1];
  }
  return checked;
};
const changeDate = () => {
  const checked = checkDate();
  if (checked) {
    // 校验日期类型不通过
    filterOptions.value.timeSegment = [];
  } else {
    invokeChartData();
  }
};
const changeDateType = () => {
  chooseDate.value = "";
  filterOptions.value.timeSegment = [];
  getChartData();
};
const setQlanOnlineTimeDistributionData = (planTimeRatio) => {
  planOnlineTimeDistributionData.value = {
    show: true,
    chartTitle: "计划上线时间分布",
    key: new Date().getTime(),
    id: "planOnlineTimeDistribution",
    title: {
      text: "",
    },
    tooltip: {
      textStyle: {},
      trigger: "item",
      formatter: (params) => {
        // params 是一个数组，包含所有被触发 action 的数据点信息
        let res =
          params.marker +
          "<span style='display:inline-block;width:5px;'></span>";
        res +=
          params.name +
          "<span style='display:inline-block;width:10px;'></span>";
        res += params.value + "%"; // 假设这里的单位是固定的，例如“单位”
        res += "<br/>";
        return res;
      },
    },
    legend: {
      itemWdith: 12,
      itemHeight: 12,
      bottom: 0,
      left: "center",
      icon: "circle",
    },
    series: [
      {
        name: "",
        type: "pie",
        radius: ["40%", "55%"],
        center: ["50%", "40%"],
        label: {
          show: true,
          formatter: (params) => {
            let res = `${params.data.name}\n${params.data.value}%`;
            return res;
          },
          align: "center",
          lineHeight: 17,
        },
        data: planTimeRatio,
      },
    ],
  };
};

const getChartData = async () => {
  const timeSegment = filterOptions.value.timeSegment;
  const data = {
    appPriority: filterOptions.value.appPriority?.join(",") || "",
    appName: filterOptions.value.appName?.trim(),
    startTime: timeSegment ? timeSegment[0] : "",
    endTime: timeSegment ? timeSegment[1] : "",
    type: filterOptions.value.type,
    source: filterOptions.value.sourceSelect["value"]?.join(",") || "",
  };
  console.log(`获取图标数据 参数 `, JSON.stringify(data));
  try {
    const result = await getOriginChartData(data);
    // 问题类型分布
    let faultCategoryRatio = [{ name: "", value: "" }];
    faultCategoryRatio = Object.keys(result.faultCategoryRatio).map((key) => ({
      name: key,
      value: (result.faultCategoryRatio[key] * 100).toFixed(2),
    }));
    let planTimeRatio = [{ name: "", value: "" }];
    planTimeRatio = Object.keys(result.planTimeRatio).map((key) => ({
      name: key,
      value: (result.planTimeRatio[key].toString() * 100).toFixed(2),
    }));
    setQuestionDistributionData(faultCategoryRatio);
    setQlanOnlineTimeDistributionData(planTimeRatio);
  } catch (e) {
    console.error("getChartData 报错", e);
  }
};

getChartData();

// 问题每月计划解决问题数&问题解决率
const solutionCountAndRate = ref({});
const fetchSolutionChart = async () => {
  const timeSegment = filterOptions.value.timeSegment;
  const data = {
    appPriority: filterOptions.value.appPriority?.join(",") || "",
    appName: filterOptions.value.appName?.trim(),
    startTime: timeSegment ? timeSegment[0] : "",
    endTime: timeSegment ? timeSegment[1] : "",
    type: "month",
  };
  const res = await getSolutionChartData(data);
  const AxisData: Array<string> = [];
  const planResolveNum: Array<number> = [];
  const planResolveRate: Array<number> = [];
  res?.forEach((item) => {
    AxisData.push(item.key);
    planResolveNum.push(item.planResolveNum);
    planResolveRate.push(Number((item.planResolveRate * 100).toFixed(2)));
  });
  solutionCountAndRate.value = {
    show: true,
    chartTitle: "问题每月计划解决问题数&问题解决率",
    key: new Date().getTime(),
    id: "solutionCountAndRate",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },
    legend: {
      itemWdith: 12,
      itemHeight: 12,
      bottom: 0,
      left: "center",
      icon: "circle",
    },
    xAxis: [
      {
        type: "category",
        data: AxisData,
      },
    ],
    yAxis: [
      {
        type: "value",
      },
      {
        type: "value",
        axisLabel: {
          formatter: "{value}%",
        },
      },
    ],
    series: [
      {
        name: "问题数",
        type: "bar",
        data: planResolveNum,
        label: {
          show: true,
          position: "top",
        },
      },
      {
        name: "问题解决率",
        type: "line",
        yAxisIndex: 1,
        label: {
          show: true,
          position: "top",
          formatter: (params) => {
            return `${params.value}%`;
          },
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + "%";
          },
        },
        data: planResolveRate,
      },
    ],
  };
};

fetchSolutionChart();

// 问题处理SLA分析
const slaAnalysis = ref({});
const fetchSLAChart = async () => {
  const timeSegment = filterOptions.value.timeSegment;
  const data = {
    appPriority: filterOptions.value.appPriority?.join(",") || "",
    appName: filterOptions.value.appName?.trim(),
    startTime: timeSegment ? timeSegment[0] : "",
    endTime: timeSegment ? timeSegment[1] : "",
    type: filterOptions.value.type,
    source: filterOptions.value.sourceSelect["value"]?.join(",") || "",
  };
  const res = await getSLAData(data);
  const xAxisData = [];
  const identifyTime = [];
  const delimitTime = [];
  const repairTime = [];
  const returnTime = [];
  res?.forEach((item) => {
    xAxisData.push(item.key);
    identifyTime.push(item.identifyTime);
    delimitTime.push(item.delimitTime);
    repairTime.push(item.repairTime);
    returnTime.push(item.returnTime);
  });
  slaAnalysis.value = {
    show: true,
    chartTitle: "问题处理SLA分析",
    key: new Date().getTime(),
    id: "slaAnalysis",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },
    legend: {
      itemWdith: 12,
      itemHeight: 12,
      bottom: 0,
      left: "center",
      icon: "circle",
    },
    xAxis: [
      {
        type: "category",
        data: xAxisData,
      },
    ],
    yAxis: {
      axisLabel: {
        formatter: "{value}天",
      },
    },
    series: [
      {
        name: "问题识别",
        type: "bar",
        data: identifyTime,

        tooltip: {
          valueFormatter: function (value) {
            return value + "天";
          },
        },
      },
      {
        name: "问题定界",
        type: "bar",
        data: delimitTime,

        tooltip: {
          valueFormatter: function (value) {
            return value + "天";
          },
        },
      },
      {
        name: "问题修复",
        type: "bar",
        data: repairTime,

        tooltip: {
          valueFormatter: function (value) {
            return value + "天";
          },
        },
      },
      {
        name: "问题回归",
        type: "bar",
        data: returnTime,

        tooltip: {
          valueFormatter: function (value) {
            return value + "天";
          },
        },
      },
    ],
  };
};
fetchSLAChart();

// 问题闭环处理趋势
const issueCountChartData = ref({});
const fetchIssueCount = async () => {
  try {
    const timeSegment = filterOptions.value.timeSegment;
    const data = {
      appPriority: filterOptions.value.appPriority?.join(",") || "",
      appName: filterOptions.value.appName?.trim(),
      startTime: timeSegment ? timeSegment[0] : "",
      endTime: timeSegment ? timeSegment[1] : "",
      type: filterOptions.value.type,
      source: filterOptions.value.sourceSelect["value"]?.join(",") || "",
    };
    const res = await getIssueCountData(data);
    const xAxisData = [];
    const newDiscoveryNum = [];
    const closeNum = [];
    const cumulativeDiscoveryNum = [];
    const cumulativeOpenNum = [];
    const cumulativeCloseNum = [];
    res?.forEach((item) => {
      xAxisData.push(item.key);
      newDiscoveryNum.push(item.newDiscoveryNum);
      closeNum.push(item.closeNum);
      cumulativeDiscoveryNum.push(item.cumulativeDiscoveryNum);
      cumulativeOpenNum.push(item.cumulativeOpenNum);
      cumulativeCloseNum.push(item.cumulativeCloseNum);
    });
    issueCountChartData.value = {
      show: true,
      chartTitle: "问题闭环处理趋势",
      key: new Date().getTime(),
      id: "issueCountChart",
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999",
          },
        },
      },
      legend: {
        itemWdith: 12,
        itemHeight: 12,
        bottom: 0,
        left: "center",
        icon: "circle",
      },
      xAxis: [
        {
          type: "category",
          data: xAxisData,
        },
      ],
      yAxis: [
        {
          type: "value",
        },
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "新增发现数",
          type: "bar",
          data: newDiscoveryNum,
        },
        {
          name: "关闭问题数",
          type: "bar",
          data: closeNum,
        },
        {
          name: "累计发现问题数",
          type: "line",
          yAxisIndex: 1,
          data: cumulativeDiscoveryNum,
        },
        {
          name: "累计未关闭总数",
          type: "line",
          yAxisIndex: 1,
          data: cumulativeOpenNum,
        },
        {
          name: "累计未关闭问题数",
          type: "line",
          yAxisIndex: 1,
          data: cumulativeCloseNum,
        },
      ],
    };
  } catch (e) {}
};
fetchIssueCount();

// 声量趋势
const volumeChartData = ref({});
const fetchVolumeChart = async () => {
  try {
    const timeSegment = filterOptions.value.timeSegment;
    const data = {
      appPriority: filterOptions.value.appPriority?.join(",") || "",
      appName: filterOptions.value.appName?.trim(),
      startTime: timeSegment ? timeSegment[0] : "",
      endTime: timeSegment ? timeSegment[1] : "",
      type: filterOptions.value.type,
      source: filterOptions.value.sourceSelect["value"]?.join(",") || "",
    };
    const res = (await getVolumeData(data)) as any[];
    const xAxisData = [];
    const originVocNum = [];
    const originClusterNum = [];
    const transformProblemNum = [];
    res?.forEach((item) => {
      xAxisData.push(item.key);
      originVocNum.push(item.originVocNum);
      originClusterNum.push(item.originClusterNum);
      transformProblemNum.push(item.transformProblemNum);
    });
    volumeChartData.value = {
      show: true,
      chartTitle: "声量趋势",
      key: new Date().getTime(),
      id: "volumeChart",
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999",
          },
        },
      },
      legend: {
        itemWdith: 12,
        itemHeight: 12,
        bottom: 0,
        left: "center",
        icon: "circle",
      },
      xAxis: [
        {
          type: "category",
          data: xAxisData,
        },
      ],
      yAxis: [
        {
          type: "value",
          // axisLabel: {
          //   formatter: "{value}万",
          // },
        },
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "声量",
          type: "line",
          data: originVocNum,
          label: {
            show: true,
            position: "top",
          },
        },
        {
          name: "原声聚类数",
          type: "line",
          yAxisIndex: 1,
          data: originClusterNum,
          label: {
            show: true,
            position: "top",
          },
        },
        {
          name: "转换问题数",
          type: "line",
          data: transformProblemNum,
          label: {
            show: true,
            position: "top",
          },
        },
      ],
    };
  } catch (e) {}
};
fetchVolumeChart();

const invokeChartData = () => {
  getChartData();
  fetchSolutionChart();
  fetchSLAChart();
  fetchIssueCount();
  fetchVolumeChart();
};
</script>

<style lang="less" scoped>
.tools-bar {
  font-size: 14px;
  display: flex;
  .select-app-type,
  .date-range,
  .input-app-name {
    width: 200px;
  }
  > * {
    margin-left: 15px;
    height: 32px;
  }
}
.chart-body {
  display: flex;
  background-color: rgb(245, 245, 245);
  flex-wrap: wrap;
  align-self: center;
  .chart-section {
    background-color: #fff;
    width: calc(33% - 61px);
    height: 348px;
    border-radius: 8px;
    margin: 12px;
    padding: 20px;
  }
}
</style>

<style>
.page-statistical .tools-bar {
  .date-range {
    width: 260px;
  }
  .radio-dateUnit {
    .el-radio-button__inner {
      background: rgba(255, 255, 255, 0.01);
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
