<template>
  <div>
    <el-card class="card-section">
      <template #header>
        <div class="card-header" @click="updateExpanded(index)">
          <el-icon v-if="level === '0000'"><View /></el-icon>
          <el-icon v-if="level === '0001'"><MessageBox /></el-icon>
          <el-icon v-if="level === '0002'"><Lock /></el-icon>
          <el-icon v-if="level === '0003'"><EditPen /></el-icon>
          <el-icon v-if="level === '0004'"><Finished /></el-icon>
          <span>{{ title }}</span>
          <el-tag
            v-if="todoNum > 0"
            :key="todoNum"
            type="danger"
            effect="dark"
            round
          >
            {{ todoNum }}
          </el-tag>

          <div class="icon-arrow" style="margin-right: 10px">
            <el-icon v-if="isExpanded"><ArrowUp /></el-icon>
            <el-icon v-else><ArrowDown /></el-icon>
          </div>
        </div>
      </template>

      <div v-if="isExpanded" class="search-result-table">
        <el-table
          class="mt-8"
          :data="tableData"
          style="width: 100%"
          resizable
          :tooltip-options="tooltipOptions"
          v-loading="tableLoading"
        >
          <el-table-column
            v-for="item in columnList"
            :key="item.prop"
            :type="item.type"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            :minWidth="item.minWidth"
            :fixed="item.fixed"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span
                v-if="item.prop !== 'config'"
                :class="item.prop === 'description' ? 'primary-font' : ''"
                @click="
                  item.prop === 'description' ? gotoDetail(scope, 'info') : ''
                "
                >{{
                  item.transTime
                    ? formatDate(scope.row[item.prop])
                    : scope.row[item.prop] || "---"
                }}</span
              >
              <div v-if="item.prop === 'config'">
                <span class="primary-font" @click="gotoDetail(scope, 'info')"
                  >查看详情</span
                >
                <span
                  @click="gotoDetail(scope, 'handle')"
                  class="ml-8 primary-font"
                  >问题处理</span
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex-box j-c-space-between a-i-center mt-12">
          <span class="pagination-total">总计：{{ todoNum }}</span>
          <div class="flex-box a-i-center">
            <el-pagination
              v-model:current-page="pageInfo.pageNo"
              :page-size="pageInfo.pageSize"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              background
              size="small"
              layout="prev, pager, next, sizes, jumper"
              :total="todoNum"
              :page-sizes="pageSizes"
            />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { defineProps, ref, toRefs, defineEmits } from "vue";

import {
  ArrowDown,
  ArrowUp,
  Finished,
  EditPen,
  Lock,
  View,
  MessageBox,
} from "@element-plus/icons-vue";
import router from "@/router/index";
import dayjs from "dayjs";
import { pageSizes } from "@/utils/constant";
const emit = defineEmits([
  "getDataList",
  "search",
  "resetSearch",
  "updateExpanded",
]);
const props = defineProps({
  title: {
    default: () => "",
    required: true,
  },
  index: {
    default: () => 0,
    required: true,
  },
  tableLoading: {
    default: () => true,
    required: true,
  },
  tableData: {
    default: () => [],
    required: true,
  },
  columnList: {
    default: () => [],
    required: true,
  },
  todoNum: {
    default: () => 0,
    required: true,
  },
  tableFilterOptions: {
    default: () => {},
    required: true,
  },
  level: {
    default: () => "",
  },
  pageInfo: {
    default: () => {},
    required: true,
  },
  isExpanded: {
    default: false,
    required: true,
  },
});

const { title, level, index, pageInfo, isExpanded } = toRefs(props);

const gotoDetail = (scope, type) => {
  router.push({
    path: "/opinion-management-detail",
    query: { id: scope.row.opinionIssueId, type },
  });
};
const handleSizeChange = (num) => {
  pageInfo.value.pageSize = num;
  pageInfo.value.pageNo = 1;
  emit("getDataList", { ...pageInfo.value, status: level.value });
};
const handleCurrentChange = (num) => {
  pageInfo.value.pageNo = num;
  emit("getDataList", { ...pageInfo.value, status: level.value });
};
const updateExpanded = (index) => {
  isExpanded.value = true;
  emit("updateExpanded", index);
};

const formatDate = (time) => {
  if (time) {
    return dayjs(time).format("YYYY-MM-DD");
  }
  return "---";
};
const tooltipOptions = {
  placement: "bottom",
};
</script>
<style lang="less" scoped>
.mt-8 {
  margin-top: 8px;
}
.mr-8 {
  margin-right: 8px;
}

.ml-8 {
  margin-left: 8px;
}

.table-head {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.table-head-left {
  display: flex;
}
.el-card.is-always-shadow {
  border-radius: 12px !important;
}
</style>

<style></style>
