export const allColumn = [
  { prop: "appName", label: "应用名称", minWidth: "100" },
  { prop: "appPackageName", label: "应用包名", minWidth: "100" },
  { prop: "importDate", label: "导入日期", minWidth: "150" },
  { prop: "timePeriod", label: "时间分区", minWidth: "150" },
  { prop: "source", label: "问题来源", minWidth: "120" },
  { prop: "originOrder", label: "祥云单号", minWidth: "200" },
  { prop: "orderType", label: "工单类型", minWidth: "120" },
  {
    prop: "faultCategory",
    label: "问题分类",
    minWidth: "120",
  },
  { prop: "productType", label: "产品类型", minWidth: "120" },
  { prop: "productModel", label: "产品机型", minWidth: "130" },
  { prop: "osVersion", label: "系统版本", minWidth: "100" },
  { prop: "apiVersion", label: "API版本", minWidth: "120" },
  { prop: "appVersion", label: "应用版本", minWidth: "100" },
  { prop: "description", label: "源声描述", minWidth: "200" },
  { prop: "reportedTime", label: "问题发生时间", minWidth: "150" },
  { prop: "closeDate", label: "关单日期", minWidth: "150" },
  { prop: "originVolume", label: "源声声量", minWidth: "100" },
  { prop: "relatedSceneId", label: "场景分类编码", minWidth: "150" },
  { prop: "relatedSceneName", label: "场景名称", minWidth: "180" },
  { prop: "reportedPerson", label: "问题提出人", minWidth: "180" },
  { prop: "suggestResolveTime", label: "建议解决时间", minWidth: "180" },
  { prop: "remark", label: "备注", minWidth: "180" },
];

export const defaultColumn = [
  { prop: "appName", label: "应用名称", minWidth: "100" },
  { prop: "importDate", label: "导入日期", minWidth: "150" },
  { prop: "timePeriod", label: "时间分区", minWidth: "150" },
  { prop: "source", label: "问题来源", minWidth: "120" },
  { prop: "originOrder", label: "祥云单号", minWidth: "200" },
  { prop: "orderType", label: "工单类型", minWidth: "120" },
  { label: "产品类型", minWidth: "120", prop: "productType" },
  { label: "产品机型", minWidth: "130", prop: "productModel" },
  { label: "系统版本", minWidth: "100", prop: "osVersion" },
  { label: "API版本", minWidth: "120", prop: "apiVersion" },
  { label: "应用版本", minWidth: "100", prop: "appVersion" },
  { label: "源声描述", minWidth: "200", prop: "description" },
  { label: "问题发生时间", minWidth: "150", prop: "reportedTime" },
  { label: "关单日期", minWidth: "150", prop: "closeDate" },
  { label: "源声声量", minWidth: "100", prop: "originVolume" },
  { label: "场景分类编码", minWidth: "150", prop: "relatedSceneId" },
  { label: "场景名称", minWidth: "180", prop: "relatedSceneName" },
  {
    prop: "faultCategory",
    label: "问题分类",
    minWidth: "120",
  },
  { prop: "reportedPerson", label: "问题提出人", minWidth: "180" },
  { prop: "suggestResolveTime", label: "建议解决时间", minWidth: "180" },
  { prop: "remark", label: "备注", minWidth: "180" },
];
