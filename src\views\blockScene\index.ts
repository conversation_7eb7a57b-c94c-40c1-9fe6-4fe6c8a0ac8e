export const BLOCK_SCENE_TABLE_COL = [{
  prop: 'appName',
  label: '应用名称',
  minWidth: 120
}, {
  prop: 'sceneId',
  label: '场景分类编码',
  minWidth: 200
}, {
  prop: "sceneName",
  label: '场景名称',
  minWidth: 250
}, {
  prop: 'automated',
  label: '源声场景类型',
  minWidth: 120
}, {
  prop: 'channelSource',
  label: '问题来源',
  minWidth: 150
}, {
  prop: 'faultCategory',
  label: '问题分类',
  minWidth: 150
}, {
  prop: "reportedPerson",
  label: "问题提出人",
  minWidth: 100
}, {
  prop: "blockPerson",
  label: "屏蔽人",
  minWidth: 120
}, {
  prop: 'blockReason',
  label: '屏蔽理由',
  minWidth: 150
}, {
  prop: 'blockRemark',
  label: '备注',
  minWidth: 150
}, {
  prop: 'blockTime',
  label: '屏蔽时间',
  minWidth: 150,
  time: true,
}]