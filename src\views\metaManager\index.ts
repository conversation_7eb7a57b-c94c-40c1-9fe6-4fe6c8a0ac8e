import {ewpService} from "@/utils/axios";
import {ref} from "vue";

export const defaultFormData = {
  appName: '',
  ewpOwner: '',
  ewpLeader: '',
  dtseOwner: '',
  dtseLeader: '',
  solutionOwner: '',
  solutionLeader: '',
  acceptanceOwner: '',
  acceptanceLeader: '',
  appPM: '',
  viewer: '',
  appType: '',
  priority: [],
  appBundle: '',
  appShape: '',
  company: '',
  represent: '',
  testOwner: ''
}

type LabelValueOption = { label: string; value: string };
const levelOptions = ref<LabelValueOption[]>([]);
const typeOptions = ref<LabelValueOption[]>([]);
export const ewpOwnerOption = ref<LabelValueOption[]>([]);
export const testOwnerOption = ref<LabelValueOption[]>([]);


export async function getLevels(): Promise<any> {
  try {
    const response: any = await ewpService.post('/app-info/app/levels');
    const list: string[] = Array.isArray(response) ? response : [];
    const mapped: LabelValueOption[] = list.map((val: string) => ({ label: val, value: val }));
    levelOptions.value.splice(0, levelOptions.value.length, ...mapped);
    return levelOptions;
  } catch (error) {
    throw error;
  }
}

export async function getTypes(): Promise<any> {
  try {
    const response: any = await ewpService.post('/app-info/app/types');
    const list: string[] = Array.isArray(response) ? response : [];
    const mapped: LabelValueOption[] = list.map((val: string) => ({ label: val, value: val }));
    typeOptions.value.splice(0, typeOptions.value.length, ...mapped);
    return typeOptions;
  } catch (error) {
    throw error;
  }
}

export async function getEwpOwner(): Promise<any> {
  try {
    const response: any = await ewpService.get('/progressInfo/getHandlers?type=ewpOwner');
    console.log('response:', response)
    const handlers: string[] = Array.isArray(response?.handlers) ? response.handlers : [];
    const mapped: LabelValueOption[] = handlers.map((val) => ({ label: val.cnNameEmpNo, value: val.account }));
    	console.log('mapped:', mapped)
    ewpOwnerOption.value.splice(0, ewpOwnerOption.value.length, ...mapped);
    return ewpOwnerOption;
  } catch (error) {
    throw error;
  }
}

export async function getTestOwner(): Promise<any> {
  try {
    const response: any = await ewpService.get('/progressInfo/getHandlers?type=testOwner');
    const handlers: string[] = Array.isArray(response?.handlers) ? response.handlers : [];
    const mapped: LabelValueOption[] = handlers.map((val: string) => ({ label: val.cnNameEmpNo, value: val.account }));
    testOwnerOption.value.splice(0, testOwnerOption.value.length, ...mapped);
    return testOwnerOption;
  } catch (error) {
    throw error;
  }
}

export const topOption = [
  { label: "TOP2000", value: "TOP2000" },
  { label: "TOP38", value: "TOP38" },
];

export const sourceOption = [
  { label: "代表处", value: "代表处" },
  { label: "互联网", value: "互联网" },
  { label: "行业系统部", value: "行业系统部" },
];

export const option = [
  { label: '接口请求数据', value: '接口请求数据' },
  { label: 'Option1', value: 'Option1' },
  { label: 'Option2', value: 'Option2' },
  { label: 'Option3', value: 'Option3' },
]

export const defaulList = [
  { label: "TOP", key: "top", list: topOption },
  { label: "清单来源", key: "sourceName", list: sourceOption },
]

export const otherList = [
  { label: "应用垂类", key: "appType", list: typeOptions.value },
  { label: "标签", key: "priority", list: levelOptions.value },
];

export const FormComponentsData = [
  {
    label: '应用名称',
    key: 'appName',
    component: 'Input',
    rules: { required: true, message: '应用名称必填！' }
  },
  {
    label: '应用形态',
    key: 'appShape',
    component: 'Input',
    rules: { required: true, message: '应用形态必填！' }
  },
  {
    label: '应用归属公司',
    key: 'company',
    component: 'Input',
    rules: { required: true, message: '应用归属公司必填！' }
  },
  {
    label: '应用垂类',
    key: 'appType',
    component: 'Select',
    option: typeOptions.value,
    rules: { required: true, message: '应用垂类必填！' }
  },
  {
    label: '标签',
    key: 'priority',
    component: 'Select',
    multiple: true,
    option: levelOptions.value,
    rules: { required: true, message: '标签必填！' }
  },
  { label: '应用包名', key: 'appBundle', component: 'Input' },
  { label: '应用归属省份', key: 'represent', component: 'Input' },
  {
    label: '问题保障责任人',
    key: 'ewpOwner',
    component: 'Select',
    option: ewpOwnerOption.value,
    multiple: true,
    rules: { required: true, message: '问题保障责任人必填！' }
  },
  {
    label: '问题保障 TL',
    key: 'ewpLeader',
    component: 'Select',
    option: ewpOwnerOption.value,
    multiple: true,
    rules: { required: true, message: '问题保障 TL必填！' }
  },
  {
    label: '问题测试责任人',
    key: 'testOwner',
    component: 'Select',
    option: testOwnerOption.value,
    multiple: true,
  },
  {
    label: 'DTSE责任人',
    key: 'dtseOwner',
    component: 'Input',
    rules: { required: true, message: 'DTSE责任人必填！' }
  },
  {
    label: 'DTSE TL',
    key: 'dtseLeader',
    component: 'Input',
    rules: { required: true, message: 'DTSE TL必填！' }
  },
  {
    label: '生态解决方案责任人',
    key: 'solutionOwner',
    component: 'Input',
    rules: { required: true, message: '生态解决方案责任人必填！' }
  },
  {
    label: '生态解决方案TL',
    key: 'solutionLeader',
    component: 'Input',
    rules: { required: true, message: '生态解决方案TL必填！' }
  },
  {
    label: '体验测试责任人',
    key: 'acceptanceOwner',
    component: 'Input',
    rules: { required: true, message: '体验测试责任人必填！' }
  },
  {
    label: '体验测试TL',
    key: 'acceptanceLeader',
    component: 'Input',
    rules: { required: true, message: '体验测试TL必填！' }
  },
  {
    label: 'BD责任人',
    key: 'bdOwner',
    component: 'Input',
    rules: { required: true, message: 'BD责任人必填！' }
  },
  {
    label: 'BD TL',
    key: 'bdLeader',
    component: 'Input',
    rules: { required: true, message: 'BD TL必填！' }
  },
  {
    label: '应用PM',
    key: 'appPM',
    component: 'Input',
    rules: { required: true, message: '应用PM必填！' }
  },
  {
    label: '应用查看员',
    key: 'viewer',
    component: 'Input',
    rules: { required: true, message: '应用查看员必填！' }
  },
];
