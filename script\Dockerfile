ARG docker_image
FROM $docker_image

MAINTAINER www.huawei.com

# DOCKER_PACKAGE_DIR、USER_NAME、GROUP_NAME需要业务修改
ARG DOCKER_PACKAGE_DIR=PartnerVOCMngPortal
ARG USER_NAME=alliance
ARG GROUP_NAME=alliance

# USER_ID、GROUP_ID不需要修改
ARG USER_ID=1001
ARG GROUP_ID=2001

# 拷贝业务软件到/opt/huawei/app
COPY $DOCKER_PACKAGE_DIR /opt/huawei/app

# /etc/passwd各字段的含义  注册名：口令：用户标识号：组标识号：用户名：用户主目录：命令解释程序
# /etc/group各字段的含义   组名: 密码: 组标识号
RUN echo "$USER_NAME:x:$USER_ID:$GROUP_ID:$USER_NAME:/home/<USER>/bin/bash" >> /etc/passwd \
&& echo "$GROUP_NAME:x:$GROUP_ID:" >> /etc/group \
&& chown -R $USER_NAME:$GROUP_NAME /opt/huawei/

WORKDIR /home/<USER>
RUN echo "umask 0077" >> /home/<USER>/.bashrc \
&& chown -R $USER_NAME:$GROUP_NAME /home/<USER>/

USER $USER_NAME

WORKDIR /home/<USER>

# 容器的启动入口
ENTRYPOINT ["sh", "/opt/huawei/app/bin/start.sh"]
