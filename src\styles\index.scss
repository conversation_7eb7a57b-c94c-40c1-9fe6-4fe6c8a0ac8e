// import dark theme
@use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;
// import no-hover style
@use "./element/no-hover.scss";
// :root {
//   --ep-color-primary: red;
// }
@use "./element/ButtonStyle.scss";
@use "./element/FormStyle.scss";
@use "./element/MenuStyle.scss";
@use "./element/TableStyle.scss";
@use "./element/BreadcrumbStyle.scss";

:root {
  /* 自定义样式表 */
  /* 多用于默认文本颜色 */
  --default-color: #191919;
  /* 多用于可点击、选中颜色 */
  --primary-color: rgb(10, 89, 247);
  /* 透明色，用于去掉颜色 */
  --transparent-color: rgba(255, 255, 255, 0);
  /* 筛选背景色中较重的颜色 */
  --screening-heavy-bg-color: rgba(232, 232, 232, 0.8);
  /* 筛选背景色中较轻的颜色 */
  --screening-light-bg-color: rgb(245, 245, 245);
  /* form线框颜色 */
  --form-border-color: rgba(17, 26, 44, 0.2);
}

body {
  font-family: Inter, system-ui, Avenir, "Helvetica Neue", Helvetica,
    "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  --el-color-primary: var(--primary-color);
  --el-text-color-regular: var(--default-color);
  /* 表单 */
  /* border-radius */
  --el-border-radius-base: 8px;
  /* 边框颜色 */
  --el-border-color: var(--form-border-color);
}

.default-font {
  font-size: 14px;
  color: var(--default-color);
}

.primary-font {
  font-size: 14px;
  color: var(--primary-color);
  cursor: pointer;
}

.flex-box {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.a-i-center {
  align-items: center;
}

.j-c-space-between {
  justify-content: space-between;
}

.fontSize-14 {
  font-size: 14px;
}

.mt-12 {
  margin-top: 12px;
}

.mr-8 {
  margin-right: 8px;
}
