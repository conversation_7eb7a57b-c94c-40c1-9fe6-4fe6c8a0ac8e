#!/bin/bash

#
# Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
#

CURRENT_PATH=$(cd $(dirname $0); pwd)
APP_ROOT=$CURRENT_PATH/..

#RASP enable
export RASP_SWITCH=yes

#enable Nuwa_Tracer
export TRACER_AGENT_SWITCH="ON"
export TRACER_MICROSERVICE_NAME="$RUNTIME_MICROSERVICE_NAME"

export LOG_HOME="/opt/huawei/logs"

echo "export HOST_IP=$RUNTIME_POD_IP" >> /home/<USER>/.bashrc
export JAVA_OPTS="$JAVA_OPTS -Djdk.tls.rejectClientInitiatedRenegotiation=true -Dnuwa.system.configLocations=classpath*:*.properties,classpath*:*.yaml"

# parse config
CfgParser="$APP_ROOT/nuwa/tools/configparser"
$CfgParser -meta "$APP_ROOT/configtemplate/meta.txt" -log "${LOG_HOME}/configparser.log"
if [ $? -ne 0 ];then
  echo "parse app configtemplate failed."
  exit 1
fi

# 等待configparser初始化完毕
sleep 20
chmod 700 -R $APP_ROOT
find $APP_ROOT/ -type f | xargs chmod 600
find $APP_ROOT/ -name "*.sh" | xargs chmod 500
chmod 700 $APP_ROOT/nuwa/tools/*

exec "$APP_ROOT"/nuwa/bin/startup.sh
