#!/bin/bash

set -ex
set -o pipefail

BASE_PATH=$(cd $(dirname $0);pwd)
PROJECT_PATH=$(cd $BASE_PATH/..;pwd)

SERVICE_NAME=$(awk -F "=" '/service_name/ {print $2}' "${BASE_PATH}"/service.ini)
ORG_NAME=$(awk -F "=" '/DOCKER_ORG_NAME/ {print $2}' "${BASE_PATH}"/service.ini)
SCOPE_NAME=$(awk -F "=" '/SCOPE/ {print $2}' "${BASE_PATH}"/service.ini)
IMAGE_NAME=$(awk -F "=" '/IMAGE_NAME/ {print $2}' "${BASE_PATH}"/service.ini)

cd $PROJECT_PATH
PACKAGE_VERSION=$(npm pkg get version|tr -d '"')
PACKAGE_VERSION=${PACKAGE_VERSION}.${ENV_PIPELINE_STARTTIME}
IMAGE_VERSION="${PACKAGE_VERSION}"

#获取x86及arm镜像的版本号
IMAGE_VERSION_X86="x86_${IMAGE_VERSION}"
IMAGE_VERSION_ARM="arm_${IMAGE_VERSION}"

#构建镜像信息存储到buildInfo.properties
echo buildVersion=${IMAGE_VERSION} > ${WORKSPACE}/buildInfo.properties
echo imageName=${IMAGE_NAME}:${IMAGE_VERSION} >> ${WORKSPACE}/buildInfo.properties
echo orgName=${ORG_NAME} >> ${WORKSPACE}/buildInfo.properties
echo scopeName=${SCOPE_NAME} >> ${WORKSPACE}/buildInfo.properties

echo dockerImageX86=${IMAGE_NAME}:${IMAGE_VERSION_X86} >> ${WORKSPACE}/buildInfo.properties
echo dockerImageARM=${IMAGE_NAME}:${IMAGE_VERSION_ARM} >> ${WORKSPACE}/buildInfo.properties
