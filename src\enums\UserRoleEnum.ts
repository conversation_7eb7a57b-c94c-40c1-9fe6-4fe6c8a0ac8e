// 数字_admin：表示是某个领域的管理员，一定要按照这个规则来设置
export enum UserRoleEnum {
  SUPER_ADMIN = 'super_admin',
  ORDER_QUALITY_ADMIN = '0_admin',
  ORDER_QUALITY_SPOT_CHECK_LEADER = '0_spot_check_leader',
  ORDER_QUALITY_SPOT_CHECK_USER = '0_spot_check_user',
  KNOWLEDGE_ADMIN = '1_admin',
  FAULT_KNOW_ADMIN = '2_admin',
  FAULT_KNOW_TRAVERSER = '2_divide_user',
  FAULT_KNOW_EXPORTER = '2_output_user',
  FAULT_KNOW_REVIEWER = '2_review_user',
  NORMAL_USER = '3_user', // 本部门人员
  SAMPLE_CODE_ADMIN = '3_admin',
  SAMPLE_CODE_REVIEWER = '3_code_reviewer',
  SAMPLE_WISH_REVIEWER = '3_wish_reviewer',
  SAMPLE_OUTGOING_ADMIN = '3_outgoing_admin',
  SAMPLE_HOSTING_ADMIN = '3_hosting_admin',
  LISTING_PROTECTION_ADMIN = '5_admin',
  TEST_ADMIN = '6_admin',
  TEST_USER = '6_user',
  PROTOTYPE_ADMIN = '7_admin',
  DEVICE_MANAGER = '7_device_admin',
  DEPARTMENT_ADMIN = '8_admin',
  DTSE_LEADER = '8_dtse_leader', // dtse领导
  BUSINESS_TRIP_MANAGER = '8_trip_admin',
  MANAGER_USER = '8_manager_user',
  OPERATION_USER = '8_operation_user',
  ONLY_VIEW_PUBLIC_OPINION = '5_only_view_public_opinion', // 仅查看部分问题
  FLY_TIGER_MEMBER = 'FLY_TIGER_MEMBER', // 飞虎队 仅工作台/知识库/样例代码/问题部分板块 可见
  ALLIANCE_COMMINITY_USER = 'alliance_comminity_user', // 联盟社区管理员
  DEVELOPER_COMMUNITY_ADMIN = 'developer_community_admin', // 开发者社区管理员
}

export enum UserRoleCNameEnum {
  SUPER_ADMIN = '超级管理员',
  ORDER_QUALITY_ADMIN = '工单质量管理员',
  ORDER_QUALITY_SPOT_CHECK_LEADER = '工单质量抽检组长',
  ORDER_QUALITY_SPOT_CHECK_USER = '工单质量抽检人',
  KNOWLEDGE_ADMIN = '知识库管理员',
  FAULT_KNOW_ADMIN = '故障知识库管理员',
  FAULT_KNOW_TRAVERSER = '故障知识库遍历员',
  FAULT_KNOW_EXPORTER = '故障知识库输出员',
  FAULT_KNOW_REVIEWER = '故障知识库评审员',
  NORMAL_USER = '用户', // 本部门人员
  SAMPLE_CODE_ADMIN = '样例代码管理员',
  SAMPLE_CODE_REVIEWER = '样例代码需求评审人',
  SAMPLE_WISH_REVIEWER = '样例代码评审人',
  SAMPLE_OUTGOING_ADMIN = '样例代码外发管理员',
  SAMPLE_HOSTING_ADMIN = '样例代码托管管理员',
  LISTING_PROTECTION_ADMIN = '上市保障管理员',
  TEST_ADMIN = '测试管理员',
  TEST_USER = '测试用户',
  PROTOTYPE_ADMIN = '样机管理员',
  DEVICE_MANAGER = '依赖设备管理员',
  DEPARTMENT_ADMIN = '部门管理员',
  DTSE_LEADER = 'DTSE Leader',
  BUSINESS_TRIP_MANAGER = '差旅管理员',
  MANAGER_USER = '主管',
  OPERATION_USER = '运营',
  ONLY_VIEW_PUBLIC_OPINION = 'BD/解决方案',
  FLY_TIGER_MEMBER = '飞虎队',
  ALLIANCE_COMMINITY_USER = '联盟社区管理员',
  DEVELOPER_COMMUNITY_ADMIN = '开发者社区管理员',
}

/*
* 定义角色权限，数字越大，权限越低
* 超级管理员角色： 0
* 管理员角色：10
* 普通角色：40，普通角色用于区分身份
* 外部用户角色：50，外部用户非本部门同学，最低权限
*/
export const ROLE_LEVELS = {
  [UserRoleEnum.SUPER_ADMIN]: 0,
  [UserRoleEnum.ORDER_QUALITY_ADMIN]: 10,
  [UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_LEADER]: 20,
  [UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_USER]: 30,
  [UserRoleEnum.KNOWLEDGE_ADMIN]: 10,
  [UserRoleEnum.FAULT_KNOW_ADMIN]: 10,
  [UserRoleEnum.FAULT_KNOW_TRAVERSER]: 20,
  [UserRoleEnum.FAULT_KNOW_EXPORTER]: 20,
  [UserRoleEnum.FAULT_KNOW_REVIEWER]: 30,
  [UserRoleEnum.NORMAL_USER]: 40,
  [UserRoleEnum.SAMPLE_CODE_ADMIN]: 10,
  [UserRoleEnum.SAMPLE_CODE_REVIEWER]: 20,
  [UserRoleEnum.SAMPLE_WISH_REVIEWER]: 20,
  [UserRoleEnum.SAMPLE_OUTGOING_ADMIN]: 10,
  [UserRoleEnum.SAMPLE_HOSTING_ADMIN]: 10,
  [UserRoleEnum.LISTING_PROTECTION_ADMIN]: 10,
  [UserRoleEnum.TEST_ADMIN]: 10,
  [UserRoleEnum.TEST_USER]: 40,
  [UserRoleEnum.PROTOTYPE_ADMIN]: 10,
  [UserRoleEnum.DEVICE_MANAGER]: 10,
  [UserRoleEnum.DEPARTMENT_ADMIN]: 10,
  [UserRoleEnum.DTSE_LEADER]: 40,
  [UserRoleEnum.BUSINESS_TRIP_MANAGER]: 10,
  [UserRoleEnum.MANAGER_USER]: 40,
  [UserRoleEnum.OPERATION_USER]: 40,
  [UserRoleEnum.ONLY_VIEW_PUBLIC_OPINION]: 50,
  [UserRoleEnum.FLY_TIGER_MEMBER]: 50,
  [UserRoleEnum.ALLIANCE_COMMINITY_USER]: 10,
  [UserRoleEnum.DEVELOPER_COMMUNITY_ADMIN]: 10,
}
