import service from '@/utils/axios';

export interface IRManageModel {
  _id: string;
  orderId: string;
  appName: string;
  ewpOwner: string;
  acceptanceOwner: string;
  irOrderStatus: string;
  irRegisTime: string;
  irOverTime: string;
}

export const queryUserList = (params: any) => {
  return service({
    url: '/user/findByPage',
    method: 'get',
    params: {
      ...params,
      pageNum: params.page || 0,
      pageSize: params.pageSize || 10,
    },
  });
};
interface BaseRsp {
  message: string;
  status: string;
}
export interface QueryUserListRsp extends BaseRsp {
  data: {
    data: UserDto[];
    pageInfo: { total: number };
  };
}
export function queryUser(params: QueryUserReq): Promise<QueryUserListRsp> {
  return service({
    url: '/user/query',
    method: 'post',
    data: params,
  });
}
export const addUser = (data) => {
  return service({
    url: '/user/add',
    method: 'post',
    data: {
      userDto: data,
    },
  });
};
export const editUser = (data: Partial<UserDto>) => {
  return service({
    url: '/user/update',
    method: 'post',
    data,
  });
};
export const deleteUser = (params: DeleteUserReq) => {
  return service<boolean>({
    url: '/user/delete',
    method: 'delete',
    params,
  });
};
export const addUserList = (file: File, module: string) => {
  let formData = new FormData();
  formData.append('file', file);
  return service({
    url: `/user/upload/${module}`,
    method: 'post',
    data: formData,
    headers: {
      'content-type': 'multipart/form-data',
    },
  });
};
/**
 * 用户导入
 */
export const importUserListAll = (data) => {
  const formData = new FormData();
  formData.append('file', data);
  return service({
    url: '/user/uploadConsole',
    method: 'post',
    data: formData,
    headers: {
      'content-type': 'multipart/form-data',
    },
  });
};
export const downloadTemplate = () => {
  return service({
    url: '/File/downloadTemplateNew',
    method: 'get',
    responseType: 'blob',
  });
};
interface QueryUserReq {
  user: Partial<UserDto>;
  pageNum: number;
  pageSize: number;
  departmentList?: string[]
}
export interface UserDto {
  userName: string;
  account: string;
  password: string;
  roles: string[];
  personalSkills: string;
  modules?: string[];
  demoCodeRole?: string;
  // 主管工号
  leader: string;
  // 主管姓名
  leaderName: string;
  // 地域
  residence: string;
  departmentList?: string[];
  team?: string;
  teamList?: string[];
}
interface DeleteUserReq {
  account: string;
}

export const getEmployeeList = () => {
  return service({
    url: `/user/queryAll`,
    method: 'get',
  });
};


// 工号变更
export const changeEmployeeService = (params) => {
  return service({
    url: '/user/renewAccount',
    method: 'post',
    params,
  });
};
