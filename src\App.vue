<script setup lang="ts">
import { useUserStore } from "./store/modules/user";
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { UNAUTHORIZED_PAGE } from "./utils/env";
import { Service } from "@element-plus/icons-vue";

const userStore = useUserStore();
const router = useRouter();

// 初始化用户信息和权限
onMounted(async () => {
  try {
    // 检查用户是否已登录
    const res = await userStore.checkLogin();
    if (res.resultCode === 0) {
      // 获取用户信息
      await userStore.getUserInfo();
      // 获取用户权限
      await userStore.getUserPermissions();
    } else {
      // 未登录，跳转到登录页
      window.location.href = UNAUTHORIZED_PAGE;
    }
  } catch (error) {
    console.error("初始化用户信息失败:", error);
  }
});
</script>

<template>
  <el-config-provider>
    <RouterView />

    <div class="affix-container">
      <el-affix>
        <el-popover
          class="box-item"
          content="问题反馈群：859986224281125213"
          placement="left-start"
        >
          <template #reference>
            <el-icon size="30"><Service /></el-icon>
          </template>
        </el-popover>
      </el-affix>
    </div>
  </el-config-provider>
</template>

<style lang="scss" scoped>
.affix-container {
  position: fixed;
  z-index: 9999;
  right: 0px;
  top: 50%;
  height: 40px;
  width: 40px;
  border-radius: 20px;
  text-align: center;
  background: #fff;
  opacity: 0.8;
  transform: scale(0.7);
  padding-top: 5px;
  box-sizing: border-box;
}
.affix-container:hover {
  opacity: 1;
  transform: scale(1);
}
</style>

<style>
#app {
  color: var(--el-text-color-primary);
  min-width: 1200px;
}

body {
  margin: 0px;
}
.el-popover.el-popper {
  font-size: 11px !important;
}

.page-title-section {
  display: flex;
  width: 100%;
  justify-content: space-between;
  .page-title {
    font-size: 20px;
  }
}
.card-section {
  border-radius: 4px !important;
  margin-top: 24px;
  .el-card__header {
    height: 40px;
    padding: 0;
    .card-header {
      position: relative;
      display: flex;
      text-align: left;
      font-size: 14px;
      height: 100%;
      padding: 0 16px 0;
      background-color: #e9e9e9;
      span {
        align-self: center;
      }
      .el-icon {
        align-self: center;
        margin-right: 8px;
      }
      .el-tag {
        margin-left: 10px;
        font-size: 11px;
      }
      .icon-arrow {
        align-self: center;
        position: absolute;
        right: 0;
      }
    }
  }

  .el-card__body {
    background-color: #fff;
    padding: 0;
    > div {
      padding: 12px;
    }
  }
}

.filter-comp {
  .el-card.is-always-shadow {
    box-shadow: unset;
    border: unset;
  }
  box-shadow: var(--el-box-shadow-light);
  .table-head {
    display: flex;
    flex-direction: column;
    padding: 20px 20px 0;
    background-color: #f7f8f9;
    .table-header-row {
      display: flex;
      margin-bottom: 15px;
      .table-head-left {
        flex: 1;
      }
      .el-form-item {
        margin-bottom: 0;
      }
      .filter-item,
      .el-range-editor.el-input__wrapper {
        width: 240px;
        box-sizing: border-box;
      }
      .select-product {
        width: 360px;
        box-sizing: border-box;
        .el-select.select-left {
          width: 150px;
        }
        .select-right {
          flex: 1;
        }
      }
    }
  }
  .preButton {
    color: #666;
    width: 110px;
    text-align: left;
    font-size: 14px;
  }
}
.search-result-table {
  margin-top: -5px;
  background-color: #fff;
  .table-head {
    padding: 20px 20px 0;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .table-head-filter .el-input {
    font-size: 13px;
  }
  .table-wrap {
    background-color: #fff;
    padding: 20px;
  }

  .el-table--border:before,
  .el-table--border:after {
    display: none;
  }
  th.el-table__cell,
  td.el-table__cell {
    border-right-color: transparent;
  }
  thead {
    font-size: 13px;
  }
  .el-popper {
    max-width: 90%;
  }
}
.filter-comp,
.search-result-table {
  .el-input__wrapper {
    border-radius: 8px;
  }
}
</style>
