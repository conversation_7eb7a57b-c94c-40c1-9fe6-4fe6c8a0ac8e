<template>
  <div class="table-head">
    <div class="table-header-row">
      <div class="table-head-left">
        <el-input
          v-model="filterObj.appName"
          placeholder="请输入应用名称"
          clearable
          class="filter-item"
          @change="search"
        />
        <el-input
          v-model="filterObj.description"
          placeholder="请输入问题描述"
          clearable
          class="filter-item ml-8"
          @change="search"
        />
        <el-button
          type="primary"
          text
          class="ml-8"
          @click="showAllInputFilter = !showAllInputFilter"
        >
          <span style="width: 50px">{{
            showAllInputFilter ? "收起" : "更多搜索"
          }}</span>
          <el-icon v-show="showAllInputFilter" class="el-icon--right"
            ><CaretTop
          /></el-icon>
          <el-icon class="el-icon--right" v-show="!showAllInputFilter"
            ><CaretBottom
          /></el-icon>
        </el-button>
        <el-button @click="search" type="primary" class="ml-8">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>
    </div>
    <div v-if="showAllInputFilter" class="flex-box table-header-row">
      <el-form-item>
        <div class="flex-box">
          <el-date-picker
            v-model="timeDuration"
            type="daterange"
            start-placeholder="创建时间"
            end-placeholder="创建时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
            class="filter-item"
            @change="search"
            unlink-panels
          />
        </div>
      </el-form-item>
      <el-form-item class="ml-8 select-product">
        <el-select
          v-model="filterObj.productType"
          clearable
          placeholder="请选择产品类型"
          class="select-left"
          style="margin-right: -1px"
          @change="productTypeChange"
        >
          <el-option
            v-for="item in productTypeList"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <el-select
          v-model="productModelList"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="1"
          no-data-text="请先选择产品机型"
          :placeholder="
            !filterObj.productType ? '请选择产品机型' : '请选择产品机型'
          "
          class="next select-right"
          @change="productModelChange"
        >
          <el-option
            style="display: flex; flex-wrap: nowrap"
            v-for="item in productMap[filterObj.productType]"
            :key="item"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, ref, toRefs, defineEmits, computed } from "vue";
import { CaretBottom, CaretTop } from "@element-plus/icons-vue";
import { productMap } from "../commonArea/checkList";
const productTypeList = computed(() => {
  return Object.keys(productMap);
});
const emit = defineEmits(["getDataList", "search", "resetSearch"]);
const props = defineProps({
  filterOptions: {
    default: () => {},
    required: true,
  },
  level: {
    default: () => "",
  },
});

const { level } = toRefs(props);

const showAllInputFilter = ref(false);

const productModelList = ref([]);
const filterObj = ref({
  appName: "",
  description: "",
  startCreateTime: "",
  endCreateTime: "",
  status: level.value,
  productType: "",
  productModel: "",
});
const productModelChange = () => {
  filterObj.value.productModel = productModelList.value.join(",");
  search();
};
const productTypeChange = () => {
  filterObj.value.productModel = "";
  productModelList.value = [];
  search();
};
const timeDuration = ref([]);

const search = () => {
  filterObj.value.startCreateTime = timeDuration.value
    ? timeDuration.value[0]
    : "";
  filterObj.value.endCreateTime = timeDuration.value
    ? timeDuration.value[1]
    : "";
  emit("search", filterObj.value);
};

const resetSearch = () => {
  Object.keys(filterObj.value).forEach((key) => {
    filterObj.value[key] = "";
  });
  timeDuration.value = [];
  productModelList.value = [];
  emit("resetSearch", filterObj.value);
};
</script>

<style lang="less" scoped>
.filter-comp {
  .table-head-left {
    display: flex;
  }
  .ml-8 {
    margin-left: 8px;
  }
}
</style>
