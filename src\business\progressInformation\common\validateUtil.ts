import {ElMessage} from "element-plus";
import {weKnowIDReg} from "@/business/progressInformation/common/regex";

export const getValidateFun = field =>{
  return (rule, value,callback) => {
    if (value.trim() === '') {
      callback && callback(new Error(field+"不能为空！"))
      return false;
    }
    callback && callback();
    return true;
  };
}

export const validateTable = (value) => {
  return value.trim() !== ''
}

/**
 * 字符串长度校验
 * @param value 字符串
 * @param strLength 限制长度
 * @returns 
 */
export const lengthVerification = (value: string, strLength: number) => {
  const str = value ? value.trim() : '';
  if (str && str.length > strLength) {
    return new Error(`长度不能超过${strLength}！`);
  }
  return true;
};