import { defineStore } from 'pinia';
import {ref} from "vue";

export const useFlowInfoStore = defineStore('flowInfo', () => {

    //ref 类似于state属性

    const flowInfo = ref({
        isAllFinished: false,
    })

    const setIsAllFinished = (isAllFinished: boolean) => {
        flowInfo.value.isAllFinished = isAllFinished;
    }

    const personList = ref([]);

    const getLastPerson = (step) =>{
        if(step===0){
            return '';
        }
        return personList.value[step-1];
    }

    const setPersonList = list => {
        personList.value = list;
    }

    return {
        flowInfo,
        setIsAllFinished,
        personList,
        getLastPerson,
        setPersonList
    }
})