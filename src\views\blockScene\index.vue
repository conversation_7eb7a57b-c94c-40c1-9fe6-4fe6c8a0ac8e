<template>
  <div class="block-scene-page">
    <div class="page-title-section">
      <div class="page-title">屏蔽场景管理</div>
    </div>
    <el-card>
      <div>
        <div style="margin-bottom: 10px; overflow: hidden">
          <el-button
            style="float: right"
            :loading="exportLoading"
            v-if="hasExportPermission"
            @click="handleExportRes"
            >导出</el-button
          >
        </div>
        <el-table :data="blockSceneTableData" style="width: 100%">
          <el-table-column
            v-for="col in BLOCK_SCENE_TABLE_COL"
            :prop="col.prop"
            :label="col.label"
            :minWidth="col.minWidth"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div v-if="col.time">{{ transTime(scope.row.blockTime) }}</div>
              <div v-else-if="col.prop === 'automated'">
                {{ scope.row[col.prop] ? "自动" : "人工" }}
              </div>
              <div v-else>
                {{ scope.row[col.prop] || "---" }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex-box j-c-space-between a-i-center mt-12">
          <span class="pagination-total">总计：{{ pagination.total }}</span>
          <div class="flex-box a-i-center">
            <el-pagination
              background
              size="small"
              v-model:current-page="pagination.pageNo"
              layout="prev, pager, next, sizes, jumper"
              :page-sizes="pageSizes"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { BLOCK_SCENE_TABLE_COL } from "./index";
import { fetchBlockScene } from "./blockSceneApi";
import { ElMessage } from "element-plus";
import { pageSizes } from "@/utils/constant";
import { csrfTokenManager } from "@/utils/CSRFTokenManager";
import { isDev, WO_AUTH } from "@/utils/env";
import { useUserStore } from "@/store/modules/user";
import { transTime } from "@/components/issuesOpinionManage/commonArea/commonMethods";

const blockSceneTableData = ref([]);
const queryFetchDate = async () => {
  try {
    const data = {
      pageNo: pagination.value.pageNo,
      pageSize: pagination.value.pageSize,
      isBlock: true,
    };
    const res = await fetchBlockScene(data);
    blockSceneTableData.value = res.list;
    pagination.value.total = res.total;
  } catch (e) {
    console.error("获取屏蔽场景列表失败 ", e);
    ElMessage.error("获取屏蔽场景列表失败");
  }
};

const pagination = ref({
  pageNo: 1,
  pageSize: 10,
  pageSizes: pageSizes,
  total: 0,
});
const handleSizeChange = (pageSize) => {
  pagination.value.pageNo = 1;
  pagination.value.pageSize = pageSize;
  queryFetchDate();
};
const handleCurrentChange = (pageNo) => {
  pagination.value.pageNo = pageNo;
  queryFetchDate();
};

queryFetchDate();

const userStore = useUserStore();
// 通用权限检查函数
const checkResourcePermission = (resourceId) => {
  if (!resourceId || !userStore.menuList) return false;
  // 检查菜单列表中是否包含指定资源ID
  return userStore.menuList.some((menu) => menu.resourceId === resourceId);
};

const exportLoading = ref(false);
// 判断是否有导出权限
const hasExportPermission = computed(() => {
  return checkResourcePermission("ExportPublicOpinion");
});

// 导出Excel
const handleExportRes = () => {
  return new Promise((resolve, reject) => {
    const req = new XMLHttpRequest();
    exportLoading.value = true;
    const queryParams = {
      pageNo: pagination.value.pageNo,
      pageSize: pagination.value.pageSize,
      isBlock: true,
    };
    req.open(
      "POST",
      `${window.location.origin}${
        import.meta.env.VITE_APP_BASE_API
      }/clusterScene/exportData`,
      true
    );
    req.responseType = "blob";
    req.setRequestHeader("Content-Type", "application/json");
    // 使用新的 CSRFTokenManager 获取 CSRF 令牌
    const csrfToken = csrfTokenManager.getToken();
    if (csrfToken) {
      req.setRequestHeader("X-CSRF-TOKEN", csrfToken);
    }
    if (isDev()) {
      req.setRequestHeader("token", atob(WO_AUTH));
    }
    req.onload = function () {
      const data = req.response;
      if (req.status === 413) {
        exportLoading.value = false;
        ElMessage.error("导出数据量过大，限制50000条。请修改查询条件后重试");
        reject();
        return;
      }

      if (data.size === 0) {
        exportLoading.value = false;
        ElMessage.error("没有数据可导出");
        reject();
        return;
      }
      const blob = new Blob([data]);
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.download = "屏蔽场景.xlsx";
      a.href = blobUrl;
      a.click();
      exportLoading.value = false;
      resolve(true);
    };
    req.send(JSON.stringify(queryParams));
  });
};
</script>

<style lang="scss" scoped>
.page-title-section {
  margin-bottom: 20px;
}
</style>
