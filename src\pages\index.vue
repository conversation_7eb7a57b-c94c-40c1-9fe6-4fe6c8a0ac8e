<template>
  <div class="dashboard-container">
    <h1 class="page-title">仪表盘</h1>
    
    <el-row :gutter="16" class="stat-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <template #header>总用户数</template>
          <div class="stat-value">1,286</div>
          <div class="stat-footer">
            <span class="stat-label">较昨日</span>
            <span class="stat-change positive">+12.5%</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <template #header>活跃用户</template>
          <div class="stat-value">865</div>
          <div class="stat-footer">
            <span class="stat-label">较昨日</span>
            <span class="stat-change positive">+5.2%</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <template #header>今日访问量</template>
          <div class="stat-value">3,721</div>
          <div class="stat-footer">
            <span class="stat-label">较昨日</span>
            <span class="stat-change positive">+8.7%</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <template #header>系统负载</template>
          <div class="stat-value">42%</div>
          <div class="stat-footer">
            <span class="stat-label">较昨日</span>
            <span class="stat-change negative">-3.1%</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="16" class="chart-row">
      <el-col :xs="24" :md="12">
        <el-card class="chart-card">
          <template #header>访问趋势</template>
          <div class="chart-placeholder">图表区域</div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card class="chart-card">
          <template #header>用户分布</template>
          <div class="chart-placeholder">图表区域</div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-card class="activity-card">
      <template #header>最近活动</template>
      <div class="activity-list">
        <div class="activity-item">
          <div class="activity-icon">
          </div>
          <div class="activity-content">
            <div class="activity-title">新用户注册</div>
            <div class="activity-desc">用户 张三 完成了注册</div>
            <div class="activity-time">10分钟前</div>
          </div>
        </div>
        
        <div class="activity-item">
          <div class="activity-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">新增文章</div>
            <div class="activity-desc">管理员发布了新文章《系统使用指南》</div>
            <div class="activity-time">30分钟前</div>
          </div>
        </div>
        
        <div class="activity-item">
          <div class="activity-icon">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">系统更新</div>
            <div class="activity-desc">系统完成了版本更新 v1.2.5</div>
            <div class="activity-time">2小时前</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Document, Setting } from '@element-plus/icons-vue'
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 16px;
  
  .page-title {
    margin-bottom: 24px;
    font-size: 24px;
    font-weight: 500;
  }
  
  .stat-cards {
    margin-bottom: 24px;
    
    .stat-card {
      margin-bottom: 16px;
      
      .stat-value {
        font-size: 28px;
        font-weight: 600;
        margin: 16px 0;
      }
      
      .stat-footer {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        
        .stat-change {
          &.positive {
            color: #52c41a;
          }
          
          &.negative {
            color: #f5222d;
          }
        }
      }
    }
  }
  
  .chart-row {
    margin-bottom: 24px;
    
    .chart-card {
      margin-bottom: 16px;
      
      .chart-placeholder {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        color: #999;
        border-radius: 4px;
      }
    }
  }
  
  .activity-card {
    .activity-list {
      .activity-item {
        display: flex;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          margin-right: 16px;
          font-size: 24px;
        }
        
        .activity-content {
          flex: 1;
          
          .activity-title {
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .activity-desc {
            color: #666;
            margin-bottom: 4px;
          }
          
          .activity-time {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}
</style>
