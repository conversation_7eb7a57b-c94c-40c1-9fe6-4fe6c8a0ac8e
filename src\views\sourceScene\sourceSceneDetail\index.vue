<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item :to="{ path: '/source-scene-manage' }"
      >源声场景管理</el-breadcrumb-item
    >
    <el-breadcrumb-item>{{ sceneName }}</el-breadcrumb-item>
  </el-breadcrumb>
  <div class="page-title-section mt-12">
    <div class="page-title">源声详情</div>
  </div>
  <div class="search-result-table">
    <div class="table-head">
      <div class="table-head-left">
        <el-form-item>
          <el-date-picker
            v-model="timeDuration"
            type="daterange"
            start-placeholder="创建时间"
            end-placeholder="创建时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
            style="width: 320px"
            unlink-panels
          />
        </el-form-item>
      </div>
      <div>
        <el-button
          :loading="exportLoading"
          @click="handleExportRes"
          v-if="hasExportPermission"
          >导出</el-button
        >
        <ColumnSettingDialog
          :allColumn="allColumn"
          :defaultColumn="defaultColumn"
          :localStorageName="LocalStorageEnum.SOURCE_SCENE_DETAIL"
          @saveHandle="setColumn"
        />
      </div>
    </div>
    <div class="table-wrap">
      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <el-table-column
          v-for="item in columnList"
          :key="item.prop"
          :type="item.type"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.minWidth"
          show-overflow-tooltip
        >
          <template #default="scope" v-if="item.prop !== 'checkbox'">
            {{ scope.row[item.prop] || "---" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="hasEditPermission || hasDeletePermission"
          prop="config"
          label="操作"
          width="100"
          fixed="right"
        >
          <template #default="scope">
            <span
              v-if="hasEditPermission"
              class="primary-font"
              @click="editData(scope.row)"
              >编辑</span
            >
            <span
              v-if="hasDeletePermission"
              class="primary-font"
              @click="delData(scope.row)"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-box j-c-space-between a-i-center mt-12">
        <span class="pagination-total">总计：{{ pagination.total }}</span>
        <div class="flex-box a-i-center">
          <el-pagination
            background
            layout="prev, pager, next, sizes, jumper"
            :page-sizes="pagination.pageSizes"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />

          <!-- v-model:pageNo="pagination.pageNo" -->
          <!-- v-model:pageSize="pagination.pageSize" -->
        </div>
      </div>
    </div>
  </div>
  <el-dialog
    title="编辑源声"
    v-model="showEdit"
    width="62.5%"
    style="min-width: 900px"
  >
    <SourceSceneEditForm
      :disabledKeys="disabledKeys"
      :rowData="editFormData"
      @submitHandle="submitHandle"
      @cancelHandle="cancelHandle"
    />
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed, reactive } from "vue";
import { useRoute } from "vue-router";
import {
  getOriginVolumeDetailById,
  updateSourceScene,
  delSourceScene,
} from "../sourceSceneManage/scenceApi";
import { defaultColumn, allColumn } from "./index";
import ColumnSettingDialog from "@/components/columnSettingsDialog/index.vue";
import { LocalStorageEnum } from "@/components/columnSettingsDialog/index";
import { storage } from "@/utils/Storage";
import { csrfTokenManager } from "@/utils/CSRFTokenManager";
import { cloneDeep } from "lodash-es";
import { useUserStore } from "@/store";
import { WO_AUTH, isDev } from "@/utils/env";
import { ElMessage, ElMessageBox } from "element-plus";
import SourceSceneEditForm from "@/views/sourceScene/sourceSceneEditForm/index.vue";
import { MultipleKeys, getDisableKeys } from "../sourceSceneEditForm/index";
const router = useRoute();
const sceneId = ref(router.query.sceneId as string);
const sceneName = ref(router.query.sceneName as string);
console.log("sceneId", sceneId.value);
const timeDuration = ref([]);
const pagination = ref({
  pageNo: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
  total: 100,
});
let loading = ref(false);
let tableData = ref([]);

const columnList = ref([]);
const setColumn = (columnData) => {
  columnList.value = [
    { prop: "checkbox", label: "", width: "40px", type: "selection" },
    ...cloneDeep(columnData),
  ];
};
const userStore = useUserStore();
const userInfo = userStore;
const exportLoading = ref(false);
// 通用权限检查函数
const checkResourcePermission = (resourceId: string) => {
  if (!resourceId || !userStore.menuList) return false;
  // 检查菜单列表中是否包含指定资源ID
  return userStore.menuList.some((menu) => menu.resourceId === resourceId);
};
// 判断是否有导出权限
const hasExportPermission = computed(() => {
  return checkResourcePermission("ExportPublicOpinion");
});
const handleExportRes = () => {
  return new Promise((resolve, reject) => {
    const req = new XMLHttpRequest();
    exportLoading.value = true;
    const queryParams = {
      pageNo: 1,
      pageSize: 99999,
      sceneId: sceneId.value,
    };
    req.open(
      "POST",
      `${window.location.origin}${
        import.meta.env.VITE_APP_BASE_API
      }/wiseoperOriginData/exportData`,
      true
    );
    req.responseType = "blob";
    req.setRequestHeader("Content-Type", "application/json");
    // 使用新的 CSRFTokenManager 获取 CSRF 令牌
    const csrfToken = csrfTokenManager.getToken();
    if (csrfToken) {
      req.setRequestHeader("X-CSRF-TOKEN", csrfToken);
    }
    if (isDev()) {
      req.setRequestHeader("token", atob(WO_AUTH));
    }
    req.onload = function () {
      const data = req.response;
      if (req.status === 413) {
        exportLoading.value = false;
        reject(new Error("导出数据量过大，限制50000条。请修改查询条件后重试"));
        return;
      }

      if (data.size === 0) {
        exportLoading.value = false;
        reject(new Error("没有数据可导出"));
        return;
      }
      const blob = new Blob([data]);
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.download = "问题源声详情导出.xlsx";
      a.href = blobUrl;
      a.click();
      exportLoading.value = false;
      resolve(true);
    };
    req.send(JSON.stringify(queryParams));
  });
};
watch(timeDuration, (data) => {
  getPageDetail();
});

const handleSizeChange = (pageSize) => {
  pagination.value.pageSize = pageSize;
  pagination.value.pageNo = 1;
  getPageDetail();
};

const handleCurrentChange = (pageNo) => {
  pagination.value.pageNo = pageNo;
  getPageDetail();
};

const getPageDetail = async () => {
  try {
    loading.value = true;
    const params = {
      pageNo: pagination.value.pageNo,
      pageSize: pagination.value.pageSize,
      sceneId: sceneId.value,
      startTime: timeDuration.value[0] || "",
      endTime: timeDuration.value[1] || "",
    };
    console.log(`获取源声场景详情参数 `, params);
    const res = await getOriginVolumeDetailById(params);
    tableData.value = res.data;
    pagination.value.total = res.total;
    pagination.value.pageNo = res.pageNo;
    pagination.value.pageSize = res.pageSize;
    loading.value = false;
  } catch (e) {
    console.error(`获取源声详情信息失败 `, e);
    loading.value = false;
    throw e;
  }
};
onMounted(() => {
  getPageDetail();
  // 设置动态列
  const localStorageClounm = storage.get(LocalStorageEnum.SOURCE_SCENE_DETAIL);
  if (localStorageClounm) {
    setColumn(localStorageClounm);
  } else {
    setColumn(defaultColumn);
  }
});
const isManual = sceneId.value.startsWith("X-"); // 通过编码判断是否手动导入源声
// 判断是否有删除权限
const hasDeletePermission = computed(() => {
  if (isManual && checkResourcePermission("DeleteSourceSound")) {
    return true;
  }
  return false;
});
// 判断是否有编辑权限
const hasEditPermission = computed(() => {
  if (isManual && checkResourcePermission("ModifySourceSound")) {
    return true;
  }
  return false;
});
const showEdit = ref(false);
const editFormData = reactive({});
const disabledKeys = ref([]);
/**
 * 编辑
 * @param rowData 列表行数据
 */
const editData = (rowData) => {
  showEdit.value = true;
  // 单独将editFormData中的字段置空
  Object.keys(editFormData).forEach((key) => {
    editFormData[key] = "";
  });
  Object.keys(rowData).forEach((key) => {
    if (MultipleKeys.includes(key)) {
      editFormData[key] = rowData[key] ? rowData[key].split(",") : [];
    } else {
      editFormData[key] = rowData[key];
    }
  });
  editFormData.sceneId = rowData.relatedSceneId; // 场景分类编码
  editFormData.sceneName = rowData.relatedSceneName; // 场景名称
  editFormData.volume = rowData.originVolume; // 源声声量
  editFormData.packageName = rowData.appPackageName; // 应用包名
  disabledKeys.value = getDisableKeys(
    [
      "appName",
      "packageName",
      "sceneName",
      "sceneId",
      "reportedPerson",
      "reportedTime",
    ],
    editFormData
  );
};
/**
 * 删除
 * @param rowData 列表行数据
 */
const delData = (rowData) => {
  // 确认删除
  ElMessageBox.confirm("确认删除该源声？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await delSourceScene({ id: rowData.id });
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      pagination.value.pageNo = 1;
      getPageDetail();
    } catch (error) {
      ElMessage({
        type: "error",
        message: error.msg || "删除数据失败!",
      });
    } finally {
      loading.value = false;
    }
  });
};
/**
 * 取消弹窗
 */
const cancelHandle = (formData) => {
  showEdit.value = false;
};
/**
 * 提交编辑信息
 * @param formData 表单数据
 */
const submitHandle = async (formData) => {
  try {
    console.log("editFormData编辑数据", formData);
    await updateSourceScene(formData);
    ElMessage({
      type: "success",
      message: "编辑成功",
    });
    getPageDetail();
    showEdit.value = false;
  } catch (error) {
    ElMessage({
      type: "error",
      message: error.msg || "数据更新失败!",
    });
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.table-head {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
}

.table-head-left {
  display: flex;
}
.ml-8 {
  margin-left: 8px;
}
.search-result-table {
  margin-top: 24px;
  border-radius: 10px;
  overflow: hidden;
  padding-top: 10px;
}
.primary-font + .primary-font {
  margin-left: 16px;
}
</style>
