import { lengthVerification } from "@/business/progressInformation/common/validateUtil";
import {
  classificationOptions,
  problemSource,
  productMap,
} from "@/components/issuesOpinionManage/commonArea/checkList";
import { cloneDeep } from "lodash-es";

export const SELECT_ALL = "ALL";

/**
 * 所有产品类型列表
 */
export const productTypeList = Object.keys(productMap);
/**
 * 所有产品机型列表
 */
export const allProductModelList = productTypeList.reduce((pre, cur) => {
  return pre.concat(productMap[cur]);
}, []);

export const EditComponentsData = [
  {
    label: "应用名称",
    key: "appName",
    component: "Input",
    rules: { required: true },
  },
  {
    label: "应用包名",
    key: "packageName",
    component: "Input",
    rules: {
      validator(rule, value, callback) {
        return lengthVerification(value, 100);
      },
    },
  },
  {
    label: "源声描述",
    key: "description",
    component: "Input",
    rules: {
      required: true,
      validator(rule, value, callback) {
        if (!value) {
          return new Error("源声描述必填！");
        }
        return lengthVerification(value, 255);
      },
    },
  },
  {
    label: "源声声量",
    key: "volume",
    component: "Input",
    rules: {
      required: true,
      validator(rule, value, callback) {
        if (!value) {
          return new Error("源声声量必填！");
        }
        if (!/^[1-9]\d*$/.test(value)) {
          return new Error("输入有误，请检查！");
        }
        if (value <= 0 || value > 2147483647) {
          return new Error("请输入1~2147483647的值！");
        }
        return true;
      },
    },
  },
  {
    label: "问题来源",
    key: "source",
    component: "Select",
    multiple: true,
    option: problemSource,
  },
  {
    label: "应用版本",
    key: "appVersion",
    component: "Input",
    rules: {
      validator(rule, value, callback) {
        return lengthVerification(value, 100);
      },
    },
  },
  {
    label: "场景名称",
    key: "sceneName",
    component: "Input",
    rules: { required: true },
  },
  {
    label: "系统版本",
    key: "osVersion",
    component: "Input",
    rules: {
      validator(rule, value, callback) {
        return lengthVerification(value, 100);
      },
    },
  },
  {
    label: "问题发生时间",
    key: "reportedTime",
    component: "DatePicker",
    disabledDate: (time) => {
      return time > new Date();
    },
  },
  {
    label: "产品类型",
    key: "productType",
    component: "Select",
    option: productTypeList.map((item) => {
      return { label: item, value: item };
    }),
    rules: { required: true, message: "产品类型必填！" },
    multiple: true,
  },
  {
    label: "产品机型",
    key: "productModel",
    component: "Select",
    multiple: true,
  },
  {
    label: "场景分类编码",
    key: "sceneId",
    component: "Input",
  },
  {
    label: "API版本",
    component: "Input",
    key: "apiVersion",
    rules: {
      validator(rule, value, callback) {
        return lengthVerification(value, 100);
      },
    },
  },
  {
    rules: { required: true, message: "问题分类必填！" },
    label: "问题分类",
    key: "faultCategory",
    component: "Select",
    option: classificationOptions,
  },
  {
    label: "问题提出人",
    key: "reportedPerson",
    component: "Input",
    rules: {
      required: true,
      validator(rule, value, callback) {
        if (!value) {
          return Error(`问题提出人必填！`);
        }
        if (!/^[\u4e00-\u9fa5]{2,6} [0-9]{8}$/.test(value)) {
          return Error(`请输入姓名+空格+工号的格式，如：张三 12345678`);
        }
        return true;
      },
    },
  },
  {
    label: "建议解决时间",
    component: "DatePicker",
    key: "suggestResolveTime",
    disabledDate: (time) => {
      return time.getTime() < Date.now() - 86400000; // 解决时间禁止选择今天以前的时间
    },
  },
  {
    key: "remark",
    label: "备注",
    component: "Input",
    rules: {
      validator(rule, value, callback) {
        return lengthVerification(value, 255);
      },
    },
  },
];

/**
 * 编辑的多选字段
 */
export const MultipleKeys = EditComponentsData.filter((v) => v.multiple).map(
  (v) => v.key
);

/**
 * 若不可编辑字段，必填且没有值，则需要可以编辑
 * @param defaultDisabledKeysList 默认不可编辑字段
 * @param fData 需要编辑的数据
 * @returns 真正不可编辑字段
 */
export const getDisableKeys = (defaultDisabledKeysList, fData) => {
  // 深拷贝，防止数据联动
  const list = cloneDeep(defaultDisabledKeysList);
  const data = cloneDeep(fData);
  // 必填字段
  const requiredKeys = EditComponentsData.filter((v) => v.rules?.required).map(
    (v) => v.key
  );
  const returnList = [];
  list.forEach((key) => {
    if (data[key] || !requiredKeys.includes(key)) {
      returnList.push(key);
    }
  });
  return returnList;
};
