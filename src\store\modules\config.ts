import { defineStore } from "pinia";
import { getOptionPath } from "@/api/view/optionDetail/detail";

export const useConfigStore = defineStore({
    id: 'config',
    state: () => ({
        tempOrderUrls: {},
    }),

    getters: {
        getTempOrderUrls() {
            return this.tempOrderUrls;
        }
    },

    actions: {
        setTempOrdeUrls(urls) {
            this.tempOrderUrls = urls;
        },
        async getOrderUrls() {
            try {
                const urls = await getOptionPath();
                urls ? this.setTempOrdeUrls(urls) : this.setTempOrdeUrls({});
                return null;
            } catch(error) {
                console.error('获取问题单路径失败:', error);
                return null;
            }
        }
    }
});