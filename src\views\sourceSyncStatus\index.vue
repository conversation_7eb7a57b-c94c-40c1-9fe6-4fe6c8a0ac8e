<template>
  <div class="page-title-section mt-12">
    <div class="page-title">问题源声同步状态</div>
    <div class="table-head-left">
      <el-upload
        v-model:file-list="fileList"
        class="upload-btn"
        :action="actionUrl"
        :headers="headersConfig"
        :on-success="uploadSuccess"
        :on-error="uploadError"
        :limit="1"
      >
        <el-button type="default">
          <el-icon style="padding-right: 5px"><Upload /></el-icon>
          TOP38数据导入
        </el-button>
      </el-upload>
      <el-button @click="handleInit">
        <el-icon style="margin-right: 8px"><Refresh /></el-icon
        >初始化场景源声</el-button
      >
    </div>
  </div>
  <!-- 导入状态 -->
  <div class="search-result-table" style="margin-top: 24px">
    <div style="margin-bottom: 10px; padding-top: 20px; margin-left: 20px">
      <span>导入状态表</span>
    </div>
    <div class="table-head">
      <div class="table-head-left">
        <el-form-item>
          <div class="preButton">同步时间</div>
          <el-date-picker
            v-model="timeDuration"
            type="daterange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
            class="next"
            style="width: 210px"
            unlink-panels
          />
        </el-form-item>
        <el-form-item prop="status" style="margin-left: 10px">
          <el-select
            v-model="syncExportResult"
            style="width: 310px"
            placeholder="请选择同步状态"
            filterable
            clearable
          >
            <el-option
              v-for="item in statusOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>

        <el-button
          type="primary"
          @click="getPageExportDetail"
          style="margin-left: 15px"
          >查询</el-button
        >
        <el-button @click="reset">重置</el-button>
      </div>
      <div>
        <el-button @click="manualSync" :loading="manualLoading">
          <el-icon style="margin-right: 8px"><Refresh /></el-icon
          >手动导入源声数据</el-button
        >
      </div>
    </div>
    <div class="table-wrap">
      <el-table :data="exportTableData" style="width: 100%">
        <el-table-column
          v-for="item in exportColumnList"
          :key="item.prop"
          :type="item.type"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          show-overflow-tooltip
        >
          <template #default="scope" v-if="item.prop !== 'checkbox'">
            {{ scope.row[item.prop] === 0 ? 0 : scope.row[item.prop] || "---" }}
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-box j-c-space-between a-i-center mt-12">
        <span class="pagination-total">总计：{{ pagination.total }}</span>
        <div class="flex-box a-i-center">
          <el-pagination
            background
            v-model:current-page="pagination.pageNo"
            layout="prev, pager, next, sizes, jumper"
            :page-sizes="pageSizes"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
  <!-- 添加导入源声数据弹窗 -->
  <el-dialog :title="'添加导入源声数据'" width="600" v-model="showExport">
    <el-form
      :model="formData"
      label-width="140"
      label-position="left"
      ref="formRef"
    >
      <el-form-item
        :label="'同步时间'"
        :rules="{ required: true, message: '同步时间必选！' }"
      >
        <el-date-picker
          v-model="syncTimeDuration"
          type="daterange"
          placeholder="请选择时间"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
          class="next"
          style="width: 210px"
          unlink-panels
        />
      </el-form-item>
    </el-form>
    <div style="text-align: end">
      <el-button @click="showExport = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </div>
  </el-dialog>
  <!-- 加工状态 -->
  <div class="search-result-table" style="margin-top: 24px">
    <div style="margin-bottom: 10px; padding-top: 20px; margin-left: 20px">
      <span>加工状态表</span>
    </div>
    <div class="table-head">
      <div class="table-head-left">
        <el-form-item>
          <div class="preButton">同步时间</div>
          <el-date-picker
            v-model="workTimeDuration"
            type="daterange"
            placeholder="请选择时间"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
            class="next"
            style="width: 210px"
            unlink-panels
          />
        </el-form-item>
        <el-form-item prop="status" style="margin-left: 10px">
          <el-select
            style="width: 310px"
            v-model="syncWorkResult"
            placeholder="请选择同步状态"
            filterable
            clearable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-button
          type="primary"
          @click="getPageWorkDetail"
          style="margin-left: 15px"
          >查询</el-button
        >
        <el-button @click="workReset">重置</el-button>
      </div>
      <div>
        <el-button @click="workManualSync" :loading="workManualLoading">
          <el-icon style="margin-right: 8px"><Refresh /></el-icon
          >手动加工源声数据</el-button
        >
      </div>
    </div>
    <div class="table-wrap">
      <el-table :data="workTableData" style="width: 100%">
        <el-table-column
          v-for="item in workColumnList"
          :key="item.prop"
          :type="item.type"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          show-overflow-tooltip
        >
          <template #default="scope" v-if="item.prop !== 'checkbox'">
            {{ scope.row[item.prop] === 0 ? 0 : scope.row[item.prop] || "---" }}
          </template>
        </el-table-column>
      </el-table>
      <div class="flex-box j-c-space-between a-i-center mt-12">
        <span class="pagination-total">总计：{{ workPagination.total }}</span>
        <div class="flex-box a-i-center">
          <el-pagination
            background
            v-model:current-page="workPagination.pageNo"
            layout="prev, pager, next, sizes, jumper"
            :total="workPagination.total"
            :page-sizes="pageSizes"
            :page-size="workPagination.pageSize"
            @size-change="handleWorkSizeChange"
            @current-change="handleWorkCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
  <!-- 添加加工源声数据弹窗 -->
  <el-dialog :title="'添加加工源声数据'" width="600" v-model="showWork">
    <el-form
      :model="formData"
      label-width="140"
      label-position="left"
      ref="workFormRef"
    >
      <el-form-item
        :label="'同步时间'"
        :rules="{ required: true, message: '同步时间必选！' }"
      >
        <el-date-picker
          v-model="syncWorkTimeDuration"
          type="daterange"
          placeholder="请选择时间"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
          class="next"
          style="width: 210px"
          unlink-panels
        />
      </el-form-item>
    </el-form>
    <div style="text-align: end">
      <el-button @click="showWork = false">取消</el-button>
      <el-button type="primary" @click="onWorkSubmit">确定</el-button>
    </div>
  </el-dialog>

  <!-- TOP38数据导入校验结果 -->
  <div class="search-result-table" style="margin-top: 24px">
    <div style="margin-bottom: 10px; padding-top: 20px; margin-left: 20px">
      <span>TOP38数据导入校验结果</span>
    </div>
    <div class="table-wrap">
      <el-table :data="top38Result" style="width: 100%">
        <el-table-column
          v-for="item in TOP38List"
          :key="item.prop"
          :prop="item.prop"
          :width="item.width"
          show-overflow-tooltip
          :label="item.label"
        >
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from "vue";
import { useRoute } from "vue-router";
import { cloneDeep } from "lodash-es";
import {
  getOriginVolumeExportDetailById,
  getOriginVolumeWorkDetailById,
} from "../sourceScene/sourceSceneManage/scenceApi";
import { Refresh, Upload } from "@element-plus/icons-vue";
import { ElDialog, ElMessage, ElMessageBox } from "element-plus";
import type { UploadUserFile } from "element-plus";
import { ewpService as service, ewpService } from "@/utils/axios";
import { isDev, WO_AUTH } from "@/utils/env";
import { csrfTokenManager } from "@/utils/CSRFTokenManager";
import { pageSizes } from "@/utils/constant";

const yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);

// 格式化日期为 xxxx-xx-xx
const year = yesterday.getFullYear();
const month = String(yesterday.getMonth() + 1).padStart(2, "0");
const day = String(yesterday.getDate()).padStart(2, "0");

let dayWhole = `${year}-${month}-${day}`;

const router = useRoute();
const sceneId = ref(router.query.sceneId as string);
const timeDuration = ref([dayWhole, dayWhole]);
const syncTimeDuration = ref([]);
const workTimeDuration = ref([dayWhole, dayWhole]);
const syncWorkTimeDuration = ref([]);

const pagination = ref({
  pageNo: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
  total: 100,
});

const workPagination = ref({
  pageNo: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
  total: 100,
});

const formData = {
  syncTimeDuration: [],
  syncWorkTimeDuration: [],
};

const statusOptions = ref([
  { label: "同步成功", value: "成功" },
  { label: "同步失败", value: "失败" },
  { label: "执行中", value: "执行中" },
]);

let loading = ref(false);
let workLoading = ref(false);
let manualLoading = ref(false);
let workManualLoading = ref(false);
let syncExportResult = ref("");
let syncWorkResult = ref("");
let exportTableData = ref([]);
let workTableData = ref([]);
const showExport = ref(false);
const formRef = ref();
const showWork = ref(false);
const workFormRef = ref();

const exportColumnList = ref([
  { prop: "date", label: "日期", width: "150px" },
  { prop: "syncStartTime", label: "开始时间", width: "180px" },
  { prop: "syncEndTime", label: "结束时间", width: "180px" },
  { prop: "syncType", label: "类型", width: "150px" },
  { prop: "syncResult", label: "结果", width: "150px" },
  { prop: "operator", label: "操作人", width: "150px" },
  { prop: "errorLog", label: "错误日志" },
  { prop: "syncNumber", label: "导入源声", width: "160px" },
]);

const workColumnList = ref([
  { prop: "date", label: "日期", width: "150px" },
  { prop: "syncStartTime", label: "开始时间", width: "180px" },
  { prop: "syncEndTime", label: "结束时间", width: "180px" },
  { prop: "syncType", label: "类型", width: "150px" },
  { prop: "syncResult", label: "结果", width: "150px" },
  { prop: "operator", label: "操作人", width: "150px" },
  { prop: "errorLog", label: "错误日志" },
  { prop: "addNumber", label: "新增场景", width: "150px" },
  { prop: "updateNumber", label: "加工场景", width: "150px" },
]);

const TOP38List = ref([
  { prop: "id", label: "数据的序号", width: "150px" },
  { prop: "field", label: "字段", width: "250px" },
  { prop: "value", label: "当前值", width: "250px" },
  { prop: "msg", label: "校验结果" },
]);

const handleInit = (row) => {
  ElMessageBox.confirm("确定要初始化场景源声吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await service.post(`/wiseoperOriginData/initOriginData`);
        ElMessage({
          type: "success",
          message: "初始化成功!",
        });
        getPageExportDetail();
        getPageWorkDetail();
      } catch (error) {
        ElMessage({
          type: "error",
          message: "初始化失败!",
        });
      }
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消初始化",
      });
    });
};

watch(timeDuration, (data) => {
  getPageExportDetail();
});

watch(workTimeDuration, (data) => {
  getPageWorkDetail();
});

const handleSizeChange = (pageSize) => {
  pagination.value.pageNo = 1;
  pagination.value.pageSize = pageSize;
  getPageExportDetail();
};

const handleCurrentChange = (pageNo) => {
  pagination.value.pageNo = pageNo;
  getPageExportDetail();
};

const handleWorkSizeChange = (pageSize) => {
  workPagination.value.pageNo = 1;
  workPagination.value.pageSize = pageSize;
  getPageWorkDetail();
};

const handleWorkCurrentChange = (pageNo) => {
  workPagination.value.pageNo = pageNo;
  getPageWorkDetail();
};

// 导入状态
const getPageExportDetail = async () => {
  try {
    const params = {
      pageNo: pagination.value.pageNo,
      pageSize: pagination.value.pageSize,
      sceneId: sceneId.value,
      startTime: timeDuration.value[0],
      endTime: timeDuration.value[1],
      syncResult: syncExportResult.value,
    };

    const res = await getOriginVolumeExportDetailById(params);
    exportTableData.value = res.data;
    pagination.value.total = res.total;
    pagination.value.pageNo = res.pageNo;
    pagination.value.pageSize = res.pageSize;
    console.log(`获取导入源声场景详情参数 `, res);
  } catch (e) {
    console.error(`获取导入源声详情信息失败 `, e);
    loading.value = false;
    throw e;
  }
};

// 加工状态
const getPageWorkDetail = async () => {
  try {
    workLoading.value = true;
    const params = {
      pageNo: workPagination.value.pageNo,
      pageSize: workPagination.value.pageSize,
      sceneId: sceneId.value,
      startTime: workTimeDuration.value[0],
      endTime: workTimeDuration.value[1],
      syncResult: syncWorkResult.value,
    };
    console.log(`获取加工源声场景详情参数 `, params);

    const res = await getOriginVolumeWorkDetailById(params);
    workTableData.value = res.data;
    workPagination.value.total = res.total;
    workPagination.value.pageNo = res.pageNo;
    workPagination.value.pageSize = res.pageSize;
  } catch (e) {
    console.error(`获取加工源声详情信息失败 `, e);
    workLoading.value = false;
    throw e;
  }
};

onMounted(() => {
  getPageExportDetail();
  getPageWorkDetail();
});

// 查询重置
const reset = () => {
  timeDuration.value = [dayWhole, dayWhole];
  syncExportResult.value = "";
  pagination.value = {
    pageNo: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  };
  getPageExportDetail();
};
const workReset = () => {
  workTimeDuration.value = [dayWhole, dayWhole];
  syncWorkResult.value = "";
  workPagination.value = {
    pageNo: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
  };
  getPageWorkDetail();
};

// 确定按钮
const onSubmit = async () => {
  try {
    if (syncTimeDuration.value.length <= 0) {
      ElMessage({
        type: "error",
        message: `请先选择同步时间`,
      });
      return;
    } else {
      console.log(syncTimeDuration.value, "syncTime");
      await ewpService.post("/wiseoperOriginData/importWiseoperOriginData", {
        startTime: syncTimeDuration.value[0] || "",
        endTime: syncTimeDuration.value[1] || "",
      });
      showExport.value = false;
      syncTimeDuration.value = [];
      getPageExportDetail();
      ElMessage({
        type: "success",
        message: `添加数据成功`,
      });
    }
  } catch (error) {
    ElMessage({
      type: "error",
      message: `添加数据失败`,
    });
  }
};

const onWorkSubmit = async () => {
  try {
    if (syncWorkTimeDuration.value.length <= 0) {
      ElMessage({
        type: "error",
        message: `请先选择同步时间`,
      });
      return;
    } else {
      console.log(syncWorkTimeDuration.value, "syncTime");
      await ewpService.post("/clusterScene/clusterOriginData", {
        startTime: syncWorkTimeDuration.value[0] || "",
        endTime: syncWorkTimeDuration.value[1] || "",
      });
      showWork.value = false;
      syncWorkTimeDuration.value = [];
      getPageWorkDetail();
      ElMessage({
        type: "success",
        message: `添加数据成功`,
      });
    }
  } catch (error) {
    ElMessage({
      type: "error",
      message: `添加数据失败`,
    });
  }
};

// 同步按钮
const manualSync = () => {
  console.log("手动同步");
  showExport.value = true;
};
const workManualSync = () => {
  showWork.value = true;
  console.log("手动同步");
};

const fileList = ref<UploadUserFile[]>([]);
const actionUrl = `${
  import.meta.env.VITE_APP_BASE_API
}/importTop38/importOriginData`;
const headersConfig = {};
const getHeaderConfigs = () => {
  // 使用新的 CSRFTokenManager 获取 CSRF 令牌
  const csrfToken = csrfTokenManager.getToken();
  if (csrfToken) {
    headersConfig["X-CSRF-TOKEN"] = csrfToken;
  }
  if (isDev()) {
    headersConfig.token = atob(WO_AUTH);
  }
};
getHeaderConfigs();

const top38Result = ref([]);
const uploadSuccess = (response) => {
  console.log(`Top38数据导入成功！`, response);
  if (response.code === 200 && response.data) {
    top38Result.value = response.data;
  } else {
    top38Result.value = [];
  }
};

const uploadError = (e) => {
  console.log(`Top38数据导入失败 e = `, e);
  top38Result.value = [];
};
</script>

<style lang="scss" scoped>
.search-result-table {
  margin-top: 24px;
  border-radius: 10px;
  overflow: hidden;
  padding-top: 10px;
}
.table-head {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.table-head-left {
  display: flex;
}
.ml-8 {
  margin-left: 8px;
}
:deep(.el-select__wrapper) {
  border-radius: 8px 8px 8px 8px;
}
.upload-btn {
  margin-right: 10px;
}
</style>
