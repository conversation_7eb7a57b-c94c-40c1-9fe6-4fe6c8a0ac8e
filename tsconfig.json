{"compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["esnext", "dom"], "useDefineForClassFields": true, "baseUrl": ".", "module": "esnext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"], "/#/*": ["types/*"]}, "resolveJsonModule": true, "noImplicitAny": false, "types": ["vite/client", "unplugin-vue-router/client"], "strict": true, "sourceMap": true, "esModuleInterop": true, "skipLibCheck": true}, "vueCompilerOptions": {"target": 3}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "components.d.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}