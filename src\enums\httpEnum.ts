// 请求结果集
export enum ResultEnum {
  SUCCESS = 200,
  ERROR = -1,
  TIMEOUT = 10042,
  TYPE = 'success',
}

// 请求方法
export enum RequestEnum {
  PATCH = 'PATCH',
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
}

// 常用的contentTyp类型
export enum ContentTypeEnum {
  JSON = 'application/json;charset=UTF-8', // json
  TEXT = 'text/plain;charset=UTF-8', // json
  FORM_URLENCODED = 'application/x-www-form-urlencoded;charset=UTF-8', // form-data 一般配合qs
  FORM_DATA = 'multipart/form-data;charset=UTF-8', // form-data  上传
}
