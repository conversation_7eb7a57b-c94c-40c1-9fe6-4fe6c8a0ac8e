{"name": "ewp-admin", "type": "module", "version": "*********", "private": true, "packageManager": "pnpm@10.9.0", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode dev", "build:test": "vite build --mode test", "lint": "eslint .", "preview": "vite preview", "deploy:test": "deploy-cli-service deploy --mode dev"}, "dependencies": {"lodash-es": "^4.17.21", "echarts": "5.5.1", "@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^11.0.3", "element-plus": "2.9.7", "vue": "3.5.13", "axios": "1.8.4", "dayjs": "^1.9.8", "pinia": "2.0.27", "vue-router": "4.5.0", "vue-draggable-next": "^2.2.1", "wangeditor": "4.7.13", "xss": "1.0.15"}, "devDependencies": {"@antfu/eslint-config": "0.29.4", "@iconify-json/ep": "^1.1.13", "@vitejs/plugin-vue": "^5.2.1", "eslint": "^9.16.0", "less": "^4.2.0", "sass": "^1.82.0", "typescript": "^5.6.3", "unplugin-vue-components": "^0.25.1", "unplugin-vue-router": "^0.10.8", "vite": "^5.4.8", "vue-tsc": "^1.8.27"}}