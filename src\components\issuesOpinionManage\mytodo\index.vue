<template>
  <div class="my-todo-page">
    <div class="page-title-section">
      <div class="page-title">我的待办</div>
    </div>
    <!-- 筛选部分 -->
    <div class="filter-comp">
      <searchArea
        :defaulList="defaultMineList"
        :otherList="otherMineList"
        :filterOptions="filterOptions"
        @updateFilterOptions="handleFilterOptions"
      ></searchArea>
      <filterComp
        @getDataList="getDataList"
        @search="search"
        @resetSearch="resetSearch"
      ></filterComp>
    </div>
    <!-- 表格部分 -->
    <div v-if="!isEmpty">
      <div v-for="(item, index) in tabList" :key="item.level">
        <basicTable
          v-if="item.todoNum !== 0"
          :title="item.title"
          :index="index"
          :level="item.level"
          :tableLoading="item.loading"
          :todoNum="item.todoNum"
          :tableData="item.tableData"
          :columnList="item.columnList"
          :pageInfo="item.pagination"
          :isExpanded="item.isExpanded"
          @getDataList="getDataList"
          @updateExpanded="updateExpanded"
          @search="search"
        ></basicTable>
      </div>
    </div>
    <emptyView v-if="isEmpty" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, onActivated } from "vue";
import { ewpService } from "@/utils/axios";
import basicTable from "../commonArea/basicTable.vue";
import searchArea from "../commonArea/serachArea.vue";
import { defaultMineList, otherMineList } from "../commonArea/checkList";
import emptyView from "@/components/issuesOpinionManage/commonArea/empty.vue";
import filterComp from "../commonArea/filterComp.vue";

const tabList = ref([
  {
    title: "问题识别",
    todoNum: 0,
    tableData: [],
    loading: true,
    level: "0000",
    columnList: [
      { prop: "appName", label: "应用名称", width: "100px" },
      { prop: "sceneName", label: "场景名称", minWidth: "200px" },
      { prop: "opinionIssueLevel", label: "问题等级", width: "100px" },
      { prop: "severity", label: "严重程度", width: "100px" },
      { prop: "priority", label: "优先级", width: "100px" },
      { prop: "source", label: "问题来源", minWidth: "150px" },
      { prop: "totalVolume", label: "汇总声量", width: "100px" },
      { prop: "productType", label: "产品类型", width: "120px" },
      { prop: "productModel", label: "产品机型", width: "120px" },
      {
        prop: "createTime",
        label: "创建时间",
        minWidth: "150px",
        transTime: true,
      },
      { prop: "team", label: "纵队", width: "120px" },
      { prop: "appPriority", label: "应用层级", width: "120px" },
      { prop: "represent", label: "归属代表处/系统部", width: "150px" },
      { prop: "config", label: "操作", width: "200px", fixed: "right" },
      { prop: "opinionIssueId", label: "问题单号", width: "160px" },
    ],
    isExpanded: true,
    pagination: {
      pageNo: 1,
      pageSize: 10,
      total: 0,
    },
  },
  {
    title: "问题定界",
    tableData: [],
    loading: true,
    todoNum: 0,
    level: "0001",
    isExpanded: false,
    columnList: [
      { prop: "appName", label: "应用名称", width: "100px" },
      {
        label: "问题描述",
        prop: "description",
        minWidth: "240px",
        longText: true,
      },
      { prop: "opinionIssueLevel", label: "问题等级", width: "100px" },
      { prop: "priority", label: "优先级", width: "100px" },
      { prop: "source", label: "问题来源", minWidth: "150px" },
      { prop: "severity", label: "严重程度", width: "100px" },
      { prop: "totalVolume", label: "汇总声量", width: "100px" },
      { prop: "productType", label: "产品类型", width: "120px" },
      { prop: "productModel", label: "产品机型", width: "120px" },
      {
        prop: "createTime",
        label: "创建时间",
        width: "150px",
        transTime: true,
      },
      // { prop: "criticalIssue", label: "是否关键问题", width: "120px" }, // 当前工作流没有设置该字段，暂时隐藏此列
      { prop: "relatedIssueType", label: "关联单类型", width: "120px" },
      { prop: "relatedIssueId", label: "关联单号", minWidth: "160px" },
      { prop: "team", label: "纵队", width: "120px" },
      { prop: "appPriority", label: "应用层级", width: "120px" },
      { prop: "represent", label: "归属代表处/系统部", width: "150px" },
      { prop: "config", label: "操作", width: "200px", fixed: "right" },
      { prop: "opinionIssueId", label: "问题单号", width: "160px" },
    ],
    pagination: {
      pageNo: 1,
      pageSize: 10,
      total: 0,
    },
  },
  {
    title: "计划锁定",
    tableData: [],
    loading: true,
    level: "0002",
    todoNum: 0,
    isExpanded: false,
    columnList: [
      { prop: "appName", label: "应用名称", width: "100px" },
      {
        prop: "description",
        label: "问题描述",
        minWidth: "240px",
        longText: true,
      },
      { label: "问题等级", width: "100px", prop: "opinionIssueLevel" },
      { label: "优先级", width: "100px", prop: "priority" },
      { label: "问题来源", minWidth: "150px", prop: "source" },
      { prop: "severity", label: "严重程度", width: "100px" },
      { label: "汇总声量", width: "100px", prop: "totalVolume" },
      { label: "问题进展", width: "200px", prop: "progress" },
      { label: "产品类型", width: "120px", prop: "productType" },
      { label: "产品机型", width: "120px", prop: "productModel" },
      {
        label: "计划解决时间",
        prop: "planSolveTime",
        width: "120px",
        transTime: true,
      },
      {
        label: "创建时间",
        prop: "createTime",
        width: "150px",
        transTime: true,
      },
      { prop: "team", label: "纵队", width: "120px" },
      { prop: "appPriority", label: "应用层级", width: "120px" },
      { prop: "represent", label: "归属代表处/系统部", width: "150px" },
      { prop: "config", label: "操作", width: "200px", fixed: "right" },
      { prop: "opinionIssueId", label: "问题单号", width: "160px" },
    ],
    pagination: {
      total: 0,
      pageNo: 1,
      pageSize: 10,
    },
  },
  {
    title: "问题修复",
    todoNum: 0,
    level: "0003",
    tableData: [],
    loading: true,
    isExpanded: false,
    columnList: [
      { label: "应用名称", prop: "appName", width: "100px" },
      {
        label: "问题描述",
        prop: "description",
        minWidth: "240px",
        longText: true,
      },
      { label: "问题等级", prop: "opinionIssueLevel", width: "100px" },
      { label: "优先级", prop: "priority", width: "100px" },
      { label: "问题来源", prop: "source", minWidth: "150px" },
      { prop: "severity", label: "严重程度", width: "100px" },
      { label: "汇总声量", prop: "totalVolume", width: "100px" },
      { prop: "appResolveVersion", label: "问题修复版本", width: "160px" },
      { prop: "progress", label: "问题进展", width: "200px" },
      { prop: "productType", label: "产品类型", width: "120px" },
      { prop: "productModel", label: "产品机型", width: "120px" },
      {
        prop: "planSolveTime",
        label: "计划解决时间",
        width: "120px",
        transTime: true,
      },
      {
        prop: "createTime",
        label: "创建时间",
        width: "150px",
        transTime: true,
      },
      { prop: "team", label: "纵队", width: "120px" },
      { prop: "appPriority", label: "应用层级", width: "120px" },
      { prop: "represent", label: "归属代表处/系统部", width: "150px" },
      { prop: "config", label: "操作", width: "200px", fixed: "right" },
      { label: "问题单号", prop: "opinionIssueId", width: "160px" },
    ],
    pagination: {
      pageNo: 1,
      pageSize: 10,
      total: 0,
    },
  },
  {
    title: "验证闭环",
    todoNum: 0,
    level: "0004",
    tableData: [],
    loading: true,
    isExpanded: false,
    columnList: [
      { prop: "appName", label: "应用名称", width: "100px" },
      {
        prop: "description",
        label: "问题描述",
        minWidth: "240px",
        longText: true,
      },
      { prop: "opinionIssueLevel", label: "问题等级", width: "100px" },
      { prop: "priority", label: "优先级", width: "100px" },
      { prop: "source", label: "问题来源", minWidth: "150px" },
      { prop: "severity", label: "严重程度", width: "100px" },
      { prop: "totalVolume", label: "汇总声量", width: "100px" },
      { prop: "returnAppVersion", label: "回归应用版本", width: "160px" },
      { prop: "returnOsVersion", label: "回归OS版本", width: "160px" },
      { prop: "progress", label: "问题进展", width: "200px" },
      { prop: "productType", label: "产品类型", width: "120px" },
      { prop: "productModel", label: "产品机型", width: "120px" },
      {
        prop: "planSolveTime",
        label: "计划解决时间",
        width: "120px",
        transTime: true,
      },
      {
        prop: "createTime",
        label: "创建时间",
        width: "150px",
        transTime: true,
      },
      { prop: "team", label: "纵队", width: "120px" },
      { prop: "appPriority", label: "应用层级", width: "120px" },
      { prop: "represent", label: "归属代表处/系统部", width: "150px" },
      { prop: "config", label: "操作", width: "200px", fixed: "right" },
      { prop: "opinionIssueId", label: "问题单号", width: "160px" },
    ],
    pagination: {
      pageNo: 1,
      pageSize: 10,
      total: 0,
    },
  },
]);
const getDefaultFilterOptionVals = () => {
  const res = {};
  defaultMineList.concat(otherMineList).forEach((item) => {
    res[item.key] = ["all"];
  });
  return res;
};
const filterParams = ref({});
const filterOptions = ref(getDefaultFilterOptionVals());

const getFilterParams = () => {
  const res = {};
  Object.keys(filterOptions.value).forEach((key) => {
    if (!filterOptions.value[key].includes("all")) {
      res[key] = filterOptions.value[key].join(",");
    }
  });
  Object.keys(filterParams.value).forEach((key) => {
    res[key] = filterParams.value[key];
  });
  return res;
};

const handleFilterOptions = (value) => {
  filterOptions.value = value;
  getDataList();
};

onMounted(() => {
  getDataList();
});

const search = async (value) => {
  filterParams.value = value;
  getDataList();
};
const resetSearch = (value) => {
  filterOptions.value = getDefaultFilterOptionVals();
  tabList.value.forEach((tableItem) => {
    tableItem.pagination = { pageNo: 1, pageSize: 10, total: 0 };
  });
  search(value);
};

// 至少展开一个
let currentTableIndex = 0; // 当前展开table的index
const updateExpanded = (targetIndex) => {
  tabList.value.forEach((tabItem, index) => {
    if (targetIndex === index) {
      tabItem.isExpanded = true;
      currentTableIndex = targetIndex;
    } else {
      tabItem.isExpanded = false;
    }
  });
  getDataList();
};

const isEmpty = ref(false);
const getDataList = async (pageInfoValue?) => {
  try {
    let totalCount = 0;
    const paginationVal = pageInfoValue
      ? pageInfoValue
      : tabList.value[currentTableIndex].pagination;
    const data = {
      ...paginationVal,
      ...getFilterParams(),
    };
    data.status = tabList.value[currentTableIndex].level;
    const status = data.status;
    tabList.value[currentTableIndex].loading = true;
    const res = await ewpService.post(`/myTask/query`, data);
    if (status) {
      for (let i = 0; i < tabList.value.length; i++) {
        if (tabList.value[i].level === status) {
          tabList.value[i].todoNum = res.statusCountMap[status] || 0;
          tabList.value[i].tableData = res.statusDataMap[status] || [];
          tabList.value[i].pagination = {
            pageSize: paginationVal.pageSize,
            pageNo: paginationVal.pageNo,
            total: res.total,
          };
          tabList.value[i].isExpanded = true;
        } else {
          tabList.value[i].isExpanded = false;
          tabList.value[i].tableData = [];
        }
      }
    } else {
      Object.keys(res.statusDataMap).forEach((key) => {
        tabList.value.forEach((item) => {
          if (item.level === key) {
            item.tableData = res.statusDataMap[key] || [];
            item.total = res.statusDataMap[key].length || 0;
            item.pagination = {
              pageSize: 10,
              pageNo: 1,
              total: item.total,
            };
          }
        });
      });
    }
    tabList.value.forEach((item) => {
      item.todoNum = 0; // 给每个tab初始todonum为0，匹配到就赋值没匹配到就仍未0
      Object.keys(res.statusCountMap).forEach((key) => {
        if (item.level === key) {
          item.todoNum = res.statusCountMap[key] || 0;
          totalCount += item.todoNum;
        }
      });
    });
    isEmpty.value = totalCount === 0;
    if (
      tabList.value[currentTableIndex].todoNum === 0 &&
      currentTableIndex + 1 < tabList.value.length
    ) {
      currentTableIndex++;
      getDataList();
    }
  } catch (error) {
    console.error("Error fetching work order list:", error);
    isEmpty.value = true;
    throw error;
  }
  setTimeout(() => {
    tabList.value[currentTableIndex].loading = false;
  });
};
onActivated(() => {
  getDataList();
});
</script>

<style></style>
